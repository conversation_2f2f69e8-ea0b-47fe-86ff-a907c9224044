"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/incident-records/page",{

/***/ "(app-pages-browser)/./src/app/ui/incident-record/incident-record-list.tsx":
/*!*************************************************************!*\
  !*** ./src/app/ui/incident-record/incident-record-list.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _graphql_queries__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./graphql/queries */ \"(app-pages-browser)/./src/app/ui/incident-record/graphql/queries.ts\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/vessel-location-display */ \"(app-pages-browser)/./src/components/ui/vessel-location-display.tsx\");\n/* harmony import */ var _app_lib_icons_SealogsIncidentIcon__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/lib/icons/SealogsIncidentIcon */ \"(app-pages-browser)/./src/app/lib/icons/SealogsIncidentIcon.ts\");\n/* harmony import */ var _components_incident_charts_dashboard__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./components/incident-charts-dashboard */ \"(app-pages-browser)/./src/app/ui/incident-record/components/incident-charts-dashboard.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _app_lib_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/lib/icons */ \"(app-pages-browser)/./src/app/lib/icons/index.ts\");\n/* harmony import */ var _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @radix-ui/react-dropdown-menu */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-dropdown-me_8ec93851da28661231dcc925c4c21d7f/node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper functions\nconst getCrewInitials = (firstName, surname)=>{\n    var _firstName_charAt, _surname_charAt;\n    if (!firstName && !surname) return \"??\";\n    const first = (firstName === null || firstName === void 0 ? void 0 : (_firstName_charAt = firstName.charAt(0)) === null || _firstName_charAt === void 0 ? void 0 : _firstName_charAt.toUpperCase()) || \"\";\n    const last = (surname === null || surname === void 0 ? void 0 : (_surname_charAt = surname.charAt(0)) === null || _surname_charAt === void 0 ? void 0 : _surname_charAt.toUpperCase()) || \"\";\n    return \"\".concat(first).concat(last) || \"??\";\n};\n// Format names for display\nconst formatName = (person)=>{\n    // Return dash if person doesn't exist, or if id is zero\n    return person && +person.id !== 0 ? \"\".concat(person.firstName, \" \").concat(person.surname) : \"\";\n};\n// Format members to notify for display\nconst formatMembersToNotify = (members)=>{\n    if (!members || !members.nodes || members.nodes.length === 0) {\n        return \"\";\n    }\n    return members.nodes.map((member)=>\"\".concat(member.firstName, \" \").concat(member.surname)).join(\", \");\n};\nconst IncidentRecordList = ()=>{\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [incidentRecords, setIncidentRecords] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    // Get vessel icon data for proper vessel icon display\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_14__.useVesselIconData)();\n    // GraphQL query to fetch incident records\n    const [getIncidentRecords, { loading: queryIncidentRecordsLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_graphql_queries__WEBPACK_IMPORTED_MODULE_3__.GET_INCIDENT_RECORDS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            if (response && response.readIncidentRecords) {\n                setIncidentRecords(response.readIncidentRecords.nodes);\n                setIsLoading(false);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error fetching incident records:\", error);\n            setIsLoading(false);\n        }\n    });\n    // Load incident records on initial render and when filter changes\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        loadIncidentRecords();\n    }, [\n        filter\n    ]);\n    // Function to load incident records with filters\n    const loadIncidentRecords = ()=>{\n        const variables = {\n            limit: 1000\n        };\n        // Add filters if they exist\n        if (Object.keys(filter).length > 0) {\n            const graphqlFilter = {};\n            // Add vessel filter\n            if (filter.vessel && filter.vessel.value) {\n                graphqlFilter.vesselID = {\n                    eq: filter.vessel.value\n                };\n            }\n            // Add date range filter\n            if (filter.dateRange) {\n                if (filter.dateRange.startDate) {\n                    graphqlFilter.startDate = {\n                        gte: dayjs__WEBPACK_IMPORTED_MODULE_4___default()(filter.dateRange.startDate).startOf(\"day\").toISOString()\n                    };\n                }\n                if (filter.dateRange.endDate) {\n                    graphqlFilter.endDate = {\n                        lte: dayjs__WEBPACK_IMPORTED_MODULE_4___default()(filter.dateRange.endDate).endOf(\"day\").toISOString()\n                    };\n                }\n            }\n            variables.filter = graphqlFilter;\n        }\n        getIncidentRecords({\n            variables\n        });\n    };\n    // Handle filter changes\n    const handleFilterChange = (param)=>{\n        let { type, data } = param;\n        setFilter((prevFilter)=>({\n                ...prevFilter,\n                [type]: data\n            }));\n    };\n    // Create column definitions for the DataTable\n    const columns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Title\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellClassName: \"w-full tablet-lg:w-auto\",\n            cell: (param)=>{\n                let { row } = param;\n                const incident = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full space-y-2 my-2.5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    href: \"/incident-records/edit/?id=\".concat(incident.id),\n                                    className: \"hover:text-curious-blue-400\",\n                                    children: incident.title || \"-\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.P, {\n                                    className: \" desktop:hidden\",\n                                    children: formatMembersToNotify(incident.membersToNotify)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm laptop:hidden\",\n                                    children: [\n                                        (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_5__.formatDate)(incident.startDate),\n                                        \" -\",\n                                        \" \",\n                                        (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_5__.formatDate)(incident.endDate)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2.5\",\n                                    children: [\n                                        incident.vessel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"tablet-md:hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_11__.VesselLocationDisplay, {\n                                                        vessel: getVesselWithIcon(incident.vessel.id, incident.vessel),\n                                                        vesselId: incident.vessel.id,\n                                                        displayText: false\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 41\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.Separator, {\n                                                    orientation: \"vertical\",\n                                                    className: \"h-4 tablet-md:hidden\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true),\n                                        incident.reportedBy && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_10__.Avatar, {\n                                            className: \"size-8 tablet-md:hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_10__.AvatarFallback, {\n                                                children: getCrewInitials(incident.reportedBy.firstName, incident.reportedBy.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"membersToNotify\",\n            header: \"Members to Notify\",\n            cellAlignment: \"left\",\n            breakpoint: \"desktop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const incident = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: formatMembersToNotify(incident.membersToNotify)\n                }, void 0, false);\n            }\n        },\n        {\n            accessorKey: \"vessel\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Vessel\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-md\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const incident = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: incident.vessel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_11__.VesselLocationDisplay, {\n                        vessel: getVesselWithIcon(incident.vessel.id, incident.vessel),\n                        vesselId: incident.vessel.id,\n                        displayText: incident.vessel.title || \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"startDate\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Start Date\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"laptop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const incident = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_5__.formatDate)(incident.startDate)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.startDate) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.startDate) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"endDate\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"End Date\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"laptop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const incident = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_5__.formatDate)(incident.endDate)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.endDate) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.endDate) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"reportedBy\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Reported By\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 17\n                }, undefined);\n            },\n            breakpoint: \"tablet-md\",\n            cellAlignment: \"left\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const incident = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: incident.reportedBy && incident.reportedBy.id > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2.5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.Tooltip, {\n                                mobileClickable: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.TooltipTrigger, {\n                                        mobileClickable: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_10__.Avatar, {\n                                            className: \"h-8 w-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_10__.AvatarFallback, {\n                                                className: \"text-xs\",\n                                                children: getCrewInitials(incident.reportedBy.firstName, incident.reportedBy.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.TooltipContent, {\n                                        className: \"lg:hidden\",\n                                        children: formatName(incident.reportedBy)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 33\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden lg:block\",\n                                children: formatName(incident.reportedBy)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 33\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = formatName(rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.reportedBy) || \"\";\n                const valueB = formatName(rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.reportedBy) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        }\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full py-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_9__.ListHeader, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons_SealogsIncidentIcon__WEBPACK_IMPORTED_MODULE_12__.SealogsIncidentIcon, {\n                    className: \"w-8 h-8\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 23\n                }, void 0),\n                title: \"Incident Records\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.DropdownMenu, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.DropdownMenuTrigger, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_16__.SealogsCogIcon, {\n                                size: 36\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 29\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 25\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.DropdownMenuContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_18__.DropdownMenuItem, {\n                                onClick: ()=>{\n                                    router.push(\"/incident-records/create\");\n                                },\n                                children: \"New incident record\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 29\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 25\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                    lineNumber: 346,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                lineNumber: 342,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_incident_charts_dashboard__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                lineNumber: 363,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.DataTable, {\n                columns: columns,\n                data: incidentRecords,\n                noDataText: \"No incident records found\",\n                showToolbar: true,\n                isLoading: isLoading || queryIncidentRecordsLoading,\n                pageSize: 10,\n                onChange: handleFilterChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                lineNumber: 366,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n        lineNumber: 341,\n        columnNumber: 9\n    }, undefined);\n};\n_s(IncidentRecordList, \"1nsWXPP4mQoczwjopfjqAJ984NI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_14__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery\n    ];\n});\n_c = IncidentRecordList;\n/* harmony default export */ __webpack_exports__[\"default\"] = (IncidentRecordList);\nvar _c;\n$RefreshReg$(_c, \"IncidentRecordList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdWkvaW5jaWRlbnQtcmVjb3JkL2luY2lkZW50LXJlY29yZC1saXN0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFMkM7QUFDQTtBQUNFO0FBQ1c7QUFDL0I7QUFDNEI7QUFDekI7QUFFeUM7QUFDSTtBQUNqQjtBQUNPO0FBQ2dCO0FBQ047QUFDRztBQUNaO0FBVXhDO0FBQ3dCO0FBQ2dCO0FBVWhFLG1CQUFtQjtBQUNuQixNQUFNNEIsa0JBQWtCLENBQUNDLFdBQW9CQztRQUUzQkQsbUJBQ0RDO0lBRmIsSUFBSSxDQUFDRCxhQUFhLENBQUNDLFNBQVMsT0FBTztJQUNuQyxNQUFNQyxRQUFRRixDQUFBQSxzQkFBQUEsaUNBQUFBLG9CQUFBQSxVQUFXRyxNQUFNLENBQUMsZ0JBQWxCSCx3Q0FBQUEsa0JBQXNCSSxXQUFXLE9BQU07SUFDckQsTUFBTUMsT0FBT0osQ0FBQUEsb0JBQUFBLCtCQUFBQSxrQkFBQUEsUUFBU0UsTUFBTSxDQUFDLGdCQUFoQkYsc0NBQUFBLGdCQUFvQkcsV0FBVyxPQUFNO0lBQ2xELE9BQU8sR0FBV0MsT0FBUkgsT0FBYSxPQUFMRyxTQUFVO0FBQ2hDO0FBRUEsMkJBQTJCO0FBQzNCLE1BQU1DLGFBQWEsQ0FBQ0M7SUFDaEIsd0RBQXdEO0lBQ3hELE9BQU9BLFVBQVUsQ0FBQ0EsT0FBT0MsRUFBRSxLQUFLLElBQzFCLEdBQXVCRCxPQUFwQkEsT0FBT1AsU0FBUyxFQUFDLEtBQWtCLE9BQWZPLE9BQU9OLE9BQU8sSUFDckM7QUFDVjtBQUVBLHVDQUF1QztBQUN2QyxNQUFNUSx3QkFBd0IsQ0FBQ0M7SUFDM0IsSUFBSSxDQUFDQSxXQUFXLENBQUNBLFFBQVFDLEtBQUssSUFBSUQsUUFBUUMsS0FBSyxDQUFDQyxNQUFNLEtBQUssR0FBRztRQUMxRCxPQUFPO0lBQ1g7SUFDQSxPQUFPRixRQUFRQyxLQUFLLENBQ2ZFLEdBQUcsQ0FBQyxDQUFDQyxTQUFnQixHQUF1QkEsT0FBcEJBLE9BQU9kLFNBQVMsRUFBQyxLQUFrQixPQUFmYyxPQUFPYixPQUFPLEdBQzFEYyxJQUFJLENBQUM7QUFDZDtBQUVBLE1BQU1DLHFCQUFxQjs7SUFDdkIsTUFBTUMsU0FBUzlDLDBEQUFTQTtJQUN4QixNQUFNLENBQUMrQyxXQUFXQyxhQUFhLEdBQUc5QywrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUMrQyxpQkFBaUJDLG1CQUFtQixHQUFHaEQsK0NBQVFBLENBQUMsRUFBRTtJQUV6RCxNQUFNLENBQUNpRCxRQUFRQyxVQUFVLEdBQUdsRCwrQ0FBUUEsQ0FBZSxDQUFDO0lBRXBELHNEQUFzRDtJQUN0RCxNQUFNLEVBQUVtRCxpQkFBaUIsRUFBRSxHQUFHcEMsK0VBQWlCQTtJQUUvQywwQ0FBMEM7SUFDMUMsTUFBTSxDQUFDcUMsb0JBQW9CLEVBQUVDLFNBQVNDLDJCQUEyQixFQUFFLENBQUMsR0FDaEVyRCw2REFBWUEsQ0FBQ0Msa0VBQW9CQSxFQUFFO1FBQy9CcUQsYUFBYTtRQUNiQyxhQUFhLENBQUNDO1lBQ1YsSUFBSUEsWUFBWUEsU0FBU0MsbUJBQW1CLEVBQUU7Z0JBQzFDVixtQkFBbUJTLFNBQVNDLG1CQUFtQixDQUFDcEIsS0FBSztnQkFDckRRLGFBQWE7WUFDakI7UUFDSjtRQUNBYSxTQUFTLENBQUNDO1lBQ05DLFFBQVFELEtBQUssQ0FBQyxvQ0FBb0NBO1lBQ2xEZCxhQUFhO1FBQ2pCO0lBQ0o7SUFFSixrRUFBa0U7SUFDbEUvQyxnREFBU0EsQ0FBQztRQUNOK0Q7SUFDSixHQUFHO1FBQUNiO0tBQU87SUFFWCxpREFBaUQ7SUFDakQsTUFBTWEsc0JBQXNCO1FBQ3hCLE1BQU1DLFlBQWlCO1lBQ25CQyxPQUFPO1FBQ1g7UUFFQSw0QkFBNEI7UUFDNUIsSUFBSUMsT0FBT0MsSUFBSSxDQUFDakIsUUFBUVYsTUFBTSxHQUFHLEdBQUc7WUFDaEMsTUFBTTRCLGdCQUFxQixDQUFDO1lBRTVCLG9CQUFvQjtZQUNwQixJQUFJbEIsT0FBT21CLE1BQU0sSUFBSW5CLE9BQU9tQixNQUFNLENBQUNDLEtBQUssRUFBRTtnQkFDdENGLGNBQWNHLFFBQVEsR0FBRztvQkFBRUMsSUFBSXRCLE9BQU9tQixNQUFNLENBQUNDLEtBQUs7Z0JBQUM7WUFDdkQ7WUFFQSx3QkFBd0I7WUFDeEIsSUFBSXBCLE9BQU91QixTQUFTLEVBQUU7Z0JBQ2xCLElBQUl2QixPQUFPdUIsU0FBUyxDQUFDQyxTQUFTLEVBQUU7b0JBQzVCTixjQUFjTSxTQUFTLEdBQUc7d0JBQ3RCQyxLQUFLdkUsNENBQUtBLENBQUM4QyxPQUFPdUIsU0FBUyxDQUFDQyxTQUFTLEVBQ2hDRSxPQUFPLENBQUMsT0FDUkMsV0FBVztvQkFDcEI7Z0JBQ0o7Z0JBQ0EsSUFBSTNCLE9BQU91QixTQUFTLENBQUNLLE9BQU8sRUFBRTtvQkFDMUJWLGNBQWNVLE9BQU8sR0FBRzt3QkFDcEJDLEtBQUszRSw0Q0FBS0EsQ0FBQzhDLE9BQU91QixTQUFTLENBQUNLLE9BQU8sRUFDOUJFLEtBQUssQ0FBQyxPQUNOSCxXQUFXO29CQUNwQjtnQkFDSjtZQUNKO1lBRUFiLFVBQVVkLE1BQU0sR0FBR2tCO1FBQ3ZCO1FBRUFmLG1CQUFtQjtZQUFFVztRQUFVO0lBQ25DO0lBRUEsd0JBQXdCO0lBQ3hCLE1BQU1pQixxQkFBcUI7WUFBQyxFQUFFQyxJQUFJLEVBQUVDLElBQUksRUFBTztRQUMzQ2hDLFVBQVUsQ0FBQ2lDLGFBQWdCO2dCQUN2QixHQUFHQSxVQUFVO2dCQUNiLENBQUNGLEtBQUssRUFBRUM7WUFDWjtJQUNKO0lBRUEsOENBQThDO0lBQzlDLE1BQU1FLFVBQVU3RSx3RUFBYUEsQ0FBQztRQUMxQjtZQUNJOEUsYUFBYTtZQUNiQyxRQUFRO29CQUFDLEVBQUVDLE1BQU0sRUFBbUI7cUNBQ2hDLDhEQUFDL0UsbUZBQW1CQTtvQkFBQytFLFFBQVFBO29CQUFRQyxPQUFNOzs7Ozs7O1lBRS9DQyxlQUFlO1lBQ2ZDLE1BQU07b0JBQUMsRUFBRUMsR0FBRyxFQUFnQjtnQkFDeEIsTUFBTUMsV0FBV0QsSUFBSUUsUUFBUTtnQkFDN0IscUJBQ0ksOERBQUNDO29CQUFJQyxXQUFVOztzQ0FDWCw4REFBQ0Q7OzhDQUNHLDhEQUFDekYsaURBQUlBO29DQUNEMkYsTUFBTSw4QkFBMEMsT0FBWkosU0FBU3pELEVBQUU7b0NBQy9DNEQsV0FBVTs4Q0FDVEgsU0FBU0osS0FBSyxJQUFJOzs7Ozs7OENBRXZCLDhEQUFDckUsOENBQUNBO29DQUFDNEUsV0FBVTs4Q0FDUjNELHNCQUNHd0QsU0FBU0ssZUFBZTs7Ozs7Ozs7Ozs7O3NDQUlwQyw4REFBQ0g7NEJBQUlDLFdBQVU7OzhDQUNYLDhEQUFDRztvQ0FBRUgsV0FBVTs7d0NBQ1IzRixtRUFBVUEsQ0FBQ3dGLFNBQVNuQixTQUFTO3dDQUFFO3dDQUFHO3dDQUNsQ3JFLG1FQUFVQSxDQUFDd0YsU0FBU2YsT0FBTzs7Ozs7Ozs4Q0FHaEMsOERBQUNpQjtvQ0FBSUMsV0FBVTs7d0NBQ1ZILFNBQVN4QixNQUFNLGtCQUNaOzs4REFDSSw4REFBQzBCO29EQUFJQyxXQUFVOzhEQUNYLDRFQUFDbkYsMEZBQXFCQTt3REFDbEJ3RCxRQUFRakIsa0JBQ0p5QyxTQUFTeEIsTUFBTSxDQUFDakMsRUFBRSxFQUNsQnlELFNBQVN4QixNQUFNO3dEQUVuQitCLFVBQVVQLFNBQVN4QixNQUFNLENBQUNqQyxFQUFFO3dEQUM1QmlFLGFBQWE7Ozs7Ozs7Ozs7OzhEQUdyQiw4REFBQ2hGLHNEQUFTQTtvREFDTmlGLGFBQVk7b0RBQ1pOLFdBQVU7Ozs7Ozs7O3dDQUtyQkgsU0FBU1UsVUFBVSxrQkFDaEIsOERBQUM1RiwwREFBTUE7NENBQUNxRixXQUFVO3NEQUNkLDRFQUFDcEYsa0VBQWNBOzBEQUNWZSxnQkFDR2tFLFNBQVNVLFVBQVUsQ0FBQzNFLFNBQVMsRUFDN0JpRSxTQUFTVSxVQUFVLENBQUMxRSxPQUFPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVMvRDtZQUNBMkUsV0FBVyxDQUFDQyxNQUFXQztvQkFDSkQsZ0JBQ0FDO2dCQURmLE1BQU1DLFNBQVNGLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1YLFFBQVEsY0FBZFcscUNBQUFBLGVBQWdCaEIsS0FBSyxLQUFJO2dCQUN4QyxNQUFNbUIsU0FBU0YsQ0FBQUEsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTVosUUFBUSxjQUFkWSxxQ0FBQUEsZUFBZ0JqQixLQUFLLEtBQUk7Z0JBQ3hDLE9BQU9rQixPQUFPRSxhQUFhLENBQUNEO1lBQ2hDO1FBQ0o7UUFDQTtZQUNJdEIsYUFBYTtZQUNiQyxRQUFRO1lBQ1J1QixlQUFlO1lBQ2ZDLFlBQVk7WUFDWnJCLGVBQWU7WUFDZkMsTUFBTTtvQkFBQyxFQUFFQyxHQUFHLEVBQWdCO2dCQUN4QixNQUFNQyxXQUFXRCxJQUFJRSxRQUFRO2dCQUM3QixxQkFBTzs4QkFBR3pELHNCQUFzQndELFNBQVNLLGVBQWU7O1lBQzVEO1FBQ0o7UUFDQTtZQUNJWixhQUFhO1lBQ2JDLFFBQVE7b0JBQUMsRUFBRUMsTUFBTSxFQUFtQjtxQ0FDaEMsOERBQUMvRSxtRkFBbUJBO29CQUFDK0UsUUFBUUE7b0JBQVFDLE9BQU07Ozs7Ozs7WUFFL0NxQixlQUFlO1lBQ2ZDLFlBQVk7WUFDWnJCLGVBQWU7WUFDZkMsTUFBTTtvQkFBQyxFQUFFQyxHQUFHLEVBQWdCO2dCQUN4QixNQUFNQyxXQUFXRCxJQUFJRSxRQUFRO2dCQUU3QixxQkFDSTs4QkFDS0QsU0FBU3hCLE1BQU0sa0JBQ1osOERBQUN4RCwwRkFBcUJBO3dCQUNsQndELFFBQVFqQixrQkFDSnlDLFNBQVN4QixNQUFNLENBQUNqQyxFQUFFLEVBQ2xCeUQsU0FBU3hCLE1BQU07d0JBRW5CK0IsVUFBVVAsU0FBU3hCLE1BQU0sQ0FBQ2pDLEVBQUU7d0JBQzVCaUUsYUFBYVIsU0FBU3hCLE1BQU0sQ0FBQ29CLEtBQUssSUFBSTs7Ozs7OztZQUsxRDtZQUNBZSxXQUFXLENBQUNDLE1BQVdDO29CQUNKRCx1QkFBQUEsZ0JBQ0FDLHVCQUFBQTtnQkFEZixNQUFNQyxTQUFTRixDQUFBQSxpQkFBQUEsNEJBQUFBLGlCQUFBQSxLQUFNWCxRQUFRLGNBQWRXLHNDQUFBQSx3QkFBQUEsZUFBZ0JwQyxNQUFNLGNBQXRCb0MsNENBQUFBLHNCQUF3QmhCLEtBQUssS0FBSTtnQkFDaEQsTUFBTW1CLFNBQVNGLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1aLFFBQVEsY0FBZFksc0NBQUFBLHdCQUFBQSxlQUFnQnJDLE1BQU0sY0FBdEJxQyw0Q0FBQUEsc0JBQXdCakIsS0FBSyxLQUFJO2dCQUNoRCxPQUFPa0IsT0FBT0UsYUFBYSxDQUFDRDtZQUNoQztRQUNKO1FBQ0E7WUFDSXRCLGFBQWE7WUFDYkMsUUFBUTtvQkFBQyxFQUFFQyxNQUFNLEVBQW1CO3FDQUNoQyw4REFBQy9FLG1GQUFtQkE7b0JBQUMrRSxRQUFRQTtvQkFBUUMsT0FBTTs7Ozs7OztZQUUvQ3FCLGVBQWU7WUFDZkMsWUFBWTtZQUNackIsZUFBZTtZQUNmQyxNQUFNO29CQUFDLEVBQUVDLEdBQUcsRUFBZ0I7Z0JBQ3hCLE1BQU1DLFdBQVdELElBQUlFLFFBQVE7Z0JBQzdCLHFCQUFPOzhCQUFHekYsbUVBQVVBLENBQUN3RixTQUFTbkIsU0FBUzs7WUFDM0M7WUFDQThCLFdBQVcsQ0FBQ0MsTUFBV0M7b0JBQ0pELGdCQUNBQztnQkFEZixNQUFNQyxTQUFTRixDQUFBQSxpQkFBQUEsNEJBQUFBLGlCQUFBQSxLQUFNWCxRQUFRLGNBQWRXLHFDQUFBQSxlQUFnQi9CLFNBQVMsS0FBSTtnQkFDNUMsTUFBTWtDLFNBQVNGLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1aLFFBQVEsY0FBZFkscUNBQUFBLGVBQWdCaEMsU0FBUyxLQUFJO2dCQUM1QyxPQUFPaUMsT0FBT0UsYUFBYSxDQUFDRDtZQUNoQztRQUNKO1FBQ0E7WUFDSXRCLGFBQWE7WUFDYkMsUUFBUTtvQkFBQyxFQUFFQyxNQUFNLEVBQW1CO3FDQUNoQyw4REFBQy9FLG1GQUFtQkE7b0JBQUMrRSxRQUFRQTtvQkFBUUMsT0FBTTs7Ozs7OztZQUUvQ3FCLGVBQWU7WUFDZkMsWUFBWTtZQUNackIsZUFBZTtZQUNmQyxNQUFNO29CQUFDLEVBQUVDLEdBQUcsRUFBZ0I7Z0JBQ3hCLE1BQU1DLFdBQVdELElBQUlFLFFBQVE7Z0JBQzdCLHFCQUFPOzhCQUFHekYsbUVBQVVBLENBQUN3RixTQUFTZixPQUFPOztZQUN6QztZQUNBMEIsV0FBVyxDQUFDQyxNQUFXQztvQkFDSkQsZ0JBQ0FDO2dCQURmLE1BQU1DLFNBQVNGLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1YLFFBQVEsY0FBZFcscUNBQUFBLGVBQWdCM0IsT0FBTyxLQUFJO2dCQUMxQyxNQUFNOEIsU0FBU0YsQ0FBQUEsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTVosUUFBUSxjQUFkWSxxQ0FBQUEsZUFBZ0I1QixPQUFPLEtBQUk7Z0JBQzFDLE9BQU82QixPQUFPRSxhQUFhLENBQUNEO1lBQ2hDO1FBQ0o7UUFDQTtZQUNJdEIsYUFBYTtZQUNiQyxRQUFRO29CQUFDLEVBQUVDLE1BQU0sRUFBbUI7cUNBQ2hDLDhEQUFDL0UsbUZBQW1CQTtvQkFBQytFLFFBQVFBO29CQUFRQyxPQUFNOzs7Ozs7O1lBRS9Dc0IsWUFBWTtZQUNaRCxlQUFlO1lBQ2ZwQixlQUFlO1lBQ2ZDLE1BQU07b0JBQUMsRUFBRUMsR0FBRyxFQUFnQjtnQkFDeEIsTUFBTUMsV0FBV0QsSUFBSUUsUUFBUTtnQkFDN0IscUJBQ0k7OEJBQ0tELFNBQVNVLFVBQVUsSUFBSVYsU0FBU1UsVUFBVSxDQUFDbkUsRUFBRSxHQUFHLG1CQUM3Qyw4REFBQzJEO3dCQUFJQyxXQUFVOzswQ0FDWCw4REFBQzFFLG9EQUFPQTtnQ0FBQzBGLGVBQWU7O2tEQUNwQiw4REFBQ3hGLDJEQUFjQTt3Q0FBQ3dGLGVBQWU7a0RBQzNCLDRFQUFDckcsMERBQU1BOzRDQUFDcUYsV0FBVTtzREFDZCw0RUFBQ3BGLGtFQUFjQTtnREFBQ29GLFdBQVU7MERBQ3JCckUsZ0JBQ0drRSxTQUFTVSxVQUFVLENBQ2QzRSxTQUFTLEVBQ2RpRSxTQUFTVSxVQUFVLENBQUMxRSxPQUFPOzs7Ozs7Ozs7Ozs7Ozs7O2tEQUszQyw4REFBQ04sMkRBQWNBO3dDQUFDeUUsV0FBVTtrREFDckI5RCxXQUFXMkQsU0FBU1UsVUFBVTs7Ozs7Ozs7Ozs7OzBDQUd2Qyw4REFBQ1U7Z0NBQUtqQixXQUFVOzBDQUNYOUQsV0FBVzJELFNBQVNVLFVBQVU7Ozs7Ozs7Ozs7Ozs7WUFNdkQ7WUFDQUMsV0FBVyxDQUFDQyxNQUFXQztvQkFDT0QsZ0JBQ0FDO2dCQUQxQixNQUFNQyxTQUFTekUsV0FBV3VFLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1YLFFBQVEsY0FBZFcscUNBQUFBLGVBQWdCRixVQUFVLEtBQUs7Z0JBQ3pELE1BQU1LLFNBQVMxRSxXQUFXd0UsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTVosUUFBUSxjQUFkWSxxQ0FBQUEsZUFBZ0JILFVBQVUsS0FBSztnQkFDekQsT0FBT0ksT0FBT0UsYUFBYSxDQUFDRDtZQUNoQztRQUNKO0tBQ0g7SUFFRCxxQkFDSSw4REFBQ2I7UUFBSUMsV0FBVTs7MEJBQ1gsOERBQUN0RixrRUFBVUE7Z0JBQ1B3RyxvQkFBTSw4REFBQ3BHLG9GQUFtQkE7b0JBQUNrRixXQUFVOzs7Ozs7Z0JBQ3JDUCxPQUFNO2dCQUNOMEIsdUJBQ0ksOERBQUNsRyx5REFBWUE7O3NDQUNULDhEQUFDRSxnRUFBbUJBOzRCQUFDaUcsT0FBTztzQ0FDeEIsNEVBQUMzRiwyREFBY0E7Z0NBQUM0RixNQUFNOzs7Ozs7Ozs7OztzQ0FFMUIsOERBQUNuRyxnRUFBbUJBO3NDQUNoQiw0RUFBQ1EsNEVBQWdCQTtnQ0FDYjRGLFNBQVM7b0NBQ0x6RSxPQUFPMEUsSUFBSSxDQUFDO2dDQUNoQjswQ0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFTdkIsOERBQUN4Ryw4RUFBdUJBOzs7OzswQkFHeEIsOERBQUNSLGdFQUFTQTtnQkFDTjhFLFNBQVNBO2dCQUNURixNQUFNbkM7Z0JBQ053RSxZQUFXO2dCQUNYQyxhQUFhO2dCQUNiM0UsV0FBV0EsYUFBYVM7Z0JBQ3hCbUUsVUFBVTtnQkFDVkMsVUFBVTFDOzs7Ozs7Ozs7Ozs7QUFJMUI7R0F2VE1yQzs7UUFDYTdDLHNEQUFTQTtRQU9NaUIsMkVBQWlCQTtRQUkzQ2QseURBQVlBOzs7S0FaZDBDO0FBd1ROLCtEQUFlQSxrQkFBa0JBLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2FwcC91aS9pbmNpZGVudC1yZWNvcmQvaW5jaWRlbnQtcmVjb3JkLWxpc3QudHN4P2Q0ODQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXHJcblxyXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXHJcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcclxuaW1wb3J0IHsgdXNlTGF6eVF1ZXJ5IH0gZnJvbSAnQGFwb2xsby9jbGllbnQnXHJcbmltcG9ydCB7IEdFVF9JTkNJREVOVF9SRUNPUkRTIH0gZnJvbSAnLi9ncmFwaHFsL3F1ZXJpZXMnXHJcbmltcG9ydCBkYXlqcyBmcm9tICdkYXlqcydcclxuaW1wb3J0IHsgZm9ybWF0RGF0ZSB9IGZyb20gJ0AvYXBwL2hlbHBlcnMvZGF0ZUhlbHBlcidcclxuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJ1xyXG5pbXBvcnQgU2VhTG9nc0J1dHRvbiBmcm9tICdAL2NvbXBvbmVudHMvdWkvc2VhLWxvZ3MtYnV0dG9uJ1xyXG5pbXBvcnQgeyBEYXRhVGFibGUsIGNyZWF0ZUNvbHVtbnMgfSBmcm9tICdAL2NvbXBvbmVudHMvZmlsdGVyZWRUYWJsZSdcclxuaW1wb3J0IHsgRGF0YVRhYmxlU29ydEhlYWRlciB9IGZyb20gJ0AvY29tcG9uZW50cy9kYXRhLXRhYmxlLXNvcnQtaGVhZGVyJ1xyXG5pbXBvcnQgeyBMaXN0SGVhZGVyIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2xpc3QtaGVhZGVyJ1xyXG5pbXBvcnQgeyBBdmF0YXIsIEF2YXRhckZhbGxiYWNrIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2F2YXRhcidcclxuaW1wb3J0IHsgVmVzc2VsTG9jYXRpb25EaXNwbGF5IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3Zlc3NlbC1sb2NhdGlvbi1kaXNwbGF5J1xyXG5pbXBvcnQgeyBTZWFsb2dzSW5jaWRlbnRJY29uIH0gZnJvbSAnQC9hcHAvbGliL2ljb25zL1NlYWxvZ3NJbmNpZGVudEljb24nXHJcbmltcG9ydCBJbmNpZGVudENoYXJ0c0Rhc2hib2FyZCBmcm9tICcuL2NvbXBvbmVudHMvaW5jaWRlbnQtY2hhcnRzLWRhc2hib2FyZCdcclxuaW1wb3J0IHsgdXNlVmVzc2VsSWNvbkRhdGEgfSBmcm9tICdAL2FwcC9saWIvdmVzc2VsLWljb24taGVscGVyJ1xyXG5pbXBvcnQge1xyXG4gICAgRHJvcGRvd25NZW51LFxyXG4gICAgRHJvcGRvd25NZW51Q29udGVudCxcclxuICAgIERyb3Bkb3duTWVudVRyaWdnZXIsXHJcbiAgICBQLFxyXG4gICAgU2VwYXJhdG9yLFxyXG4gICAgVG9vbHRpcCxcclxuICAgIFRvb2x0aXBDb250ZW50LFxyXG4gICAgVG9vbHRpcFRyaWdnZXIsXHJcbn0gZnJvbSAnQC9jb21wb25lbnRzL3VpJ1xyXG5pbXBvcnQgeyBTZWFsb2dzQ29nSWNvbiB9IGZyb20gJ0AvYXBwL2xpYi9pY29ucydcclxuaW1wb3J0IHsgRHJvcGRvd25NZW51SXRlbSB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC1kcm9wZG93bi1tZW51J1xyXG5cclxuaW50ZXJmYWNlIFNlYXJjaEZpbHRlciB7XHJcbiAgICB2ZXNzZWw/OiBhbnlcclxuICAgIGRhdGVSYW5nZT86IHtcclxuICAgICAgICBzdGFydERhdGU6IHN0cmluZyB8IG51bGxcclxuICAgICAgICBlbmREYXRlOiBzdHJpbmcgfCBudWxsXHJcbiAgICB9XHJcbn1cclxuXHJcbi8vIEhlbHBlciBmdW5jdGlvbnNcclxuY29uc3QgZ2V0Q3Jld0luaXRpYWxzID0gKGZpcnN0TmFtZT86IHN0cmluZywgc3VybmFtZT86IHN0cmluZyk6IHN0cmluZyA9PiB7XHJcbiAgICBpZiAoIWZpcnN0TmFtZSAmJiAhc3VybmFtZSkgcmV0dXJuICc/PydcclxuICAgIGNvbnN0IGZpcnN0ID0gZmlyc3ROYW1lPy5jaGFyQXQoMCk/LnRvVXBwZXJDYXNlKCkgfHwgJydcclxuICAgIGNvbnN0IGxhc3QgPSBzdXJuYW1lPy5jaGFyQXQoMCk/LnRvVXBwZXJDYXNlKCkgfHwgJydcclxuICAgIHJldHVybiBgJHtmaXJzdH0ke2xhc3R9YCB8fCAnPz8nXHJcbn1cclxuXHJcbi8vIEZvcm1hdCBuYW1lcyBmb3IgZGlzcGxheVxyXG5jb25zdCBmb3JtYXROYW1lID0gKHBlcnNvbjogYW55KSA9PiB7XHJcbiAgICAvLyBSZXR1cm4gZGFzaCBpZiBwZXJzb24gZG9lc24ndCBleGlzdCwgb3IgaWYgaWQgaXMgemVyb1xyXG4gICAgcmV0dXJuIHBlcnNvbiAmJiArcGVyc29uLmlkICE9PSAwXHJcbiAgICAgICAgPyBgJHtwZXJzb24uZmlyc3ROYW1lfSAke3BlcnNvbi5zdXJuYW1lfWBcclxuICAgICAgICA6ICcnXHJcbn1cclxuXHJcbi8vIEZvcm1hdCBtZW1iZXJzIHRvIG5vdGlmeSBmb3IgZGlzcGxheVxyXG5jb25zdCBmb3JtYXRNZW1iZXJzVG9Ob3RpZnkgPSAobWVtYmVyczogYW55KSA9PiB7XHJcbiAgICBpZiAoIW1lbWJlcnMgfHwgIW1lbWJlcnMubm9kZXMgfHwgbWVtYmVycy5ub2Rlcy5sZW5ndGggPT09IDApIHtcclxuICAgICAgICByZXR1cm4gJydcclxuICAgIH1cclxuICAgIHJldHVybiBtZW1iZXJzLm5vZGVzXHJcbiAgICAgICAgLm1hcCgobWVtYmVyOiBhbnkpID0+IGAke21lbWJlci5maXJzdE5hbWV9ICR7bWVtYmVyLnN1cm5hbWV9YClcclxuICAgICAgICAuam9pbignLCAnKVxyXG59XHJcblxyXG5jb25zdCBJbmNpZGVudFJlY29yZExpc3QgPSAoKSA9PiB7XHJcbiAgICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKVxyXG4gICAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXHJcbiAgICBjb25zdCBbaW5jaWRlbnRSZWNvcmRzLCBzZXRJbmNpZGVudFJlY29yZHNdID0gdXNlU3RhdGUoW10pXHJcblxyXG4gICAgY29uc3QgW2ZpbHRlciwgc2V0RmlsdGVyXSA9IHVzZVN0YXRlPFNlYXJjaEZpbHRlcj4oe30pXHJcblxyXG4gICAgLy8gR2V0IHZlc3NlbCBpY29uIGRhdGEgZm9yIHByb3BlciB2ZXNzZWwgaWNvbiBkaXNwbGF5XHJcbiAgICBjb25zdCB7IGdldFZlc3NlbFdpdGhJY29uIH0gPSB1c2VWZXNzZWxJY29uRGF0YSgpXHJcblxyXG4gICAgLy8gR3JhcGhRTCBxdWVyeSB0byBmZXRjaCBpbmNpZGVudCByZWNvcmRzXHJcbiAgICBjb25zdCBbZ2V0SW5jaWRlbnRSZWNvcmRzLCB7IGxvYWRpbmc6IHF1ZXJ5SW5jaWRlbnRSZWNvcmRzTG9hZGluZyB9XSA9XHJcbiAgICAgICAgdXNlTGF6eVF1ZXJ5KEdFVF9JTkNJREVOVF9SRUNPUkRTLCB7XHJcbiAgICAgICAgICAgIGZldGNoUG9saWN5OiAnY2FjaGUtYW5kLW5ldHdvcmsnLFxyXG4gICAgICAgICAgICBvbkNvbXBsZXRlZDogKHJlc3BvbnNlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2UucmVhZEluY2lkZW50UmVjb3Jkcykge1xyXG4gICAgICAgICAgICAgICAgICAgIHNldEluY2lkZW50UmVjb3JkcyhyZXNwb25zZS5yZWFkSW5jaWRlbnRSZWNvcmRzLm5vZGVzKVxyXG4gICAgICAgICAgICAgICAgICAgIHNldElzTG9hZGluZyhmYWxzZSlcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgb25FcnJvcjogKGVycm9yKSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBpbmNpZGVudCByZWNvcmRzOicsIGVycm9yKVxyXG4gICAgICAgICAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0pXHJcblxyXG4gICAgLy8gTG9hZCBpbmNpZGVudCByZWNvcmRzIG9uIGluaXRpYWwgcmVuZGVyIGFuZCB3aGVuIGZpbHRlciBjaGFuZ2VzXHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIGxvYWRJbmNpZGVudFJlY29yZHMoKVxyXG4gICAgfSwgW2ZpbHRlcl0pXHJcblxyXG4gICAgLy8gRnVuY3Rpb24gdG8gbG9hZCBpbmNpZGVudCByZWNvcmRzIHdpdGggZmlsdGVyc1xyXG4gICAgY29uc3QgbG9hZEluY2lkZW50UmVjb3JkcyA9ICgpID0+IHtcclxuICAgICAgICBjb25zdCB2YXJpYWJsZXM6IGFueSA9IHtcclxuICAgICAgICAgICAgbGltaXQ6IDEwMDAsIC8vIEZldGNoIG1vcmUgcmVjb3JkcyBmb3IgY2xpZW50LXNpZGUgcGFnaW5hdGlvblxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gQWRkIGZpbHRlcnMgaWYgdGhleSBleGlzdFxyXG4gICAgICAgIGlmIChPYmplY3Qua2V5cyhmaWx0ZXIpLmxlbmd0aCA+IDApIHtcclxuICAgICAgICAgICAgY29uc3QgZ3JhcGhxbEZpbHRlcjogYW55ID0ge31cclxuXHJcbiAgICAgICAgICAgIC8vIEFkZCB2ZXNzZWwgZmlsdGVyXHJcbiAgICAgICAgICAgIGlmIChmaWx0ZXIudmVzc2VsICYmIGZpbHRlci52ZXNzZWwudmFsdWUpIHtcclxuICAgICAgICAgICAgICAgIGdyYXBocWxGaWx0ZXIudmVzc2VsSUQgPSB7IGVxOiBmaWx0ZXIudmVzc2VsLnZhbHVlIH1cclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLy8gQWRkIGRhdGUgcmFuZ2UgZmlsdGVyXHJcbiAgICAgICAgICAgIGlmIChmaWx0ZXIuZGF0ZVJhbmdlKSB7XHJcbiAgICAgICAgICAgICAgICBpZiAoZmlsdGVyLmRhdGVSYW5nZS5zdGFydERhdGUpIHtcclxuICAgICAgICAgICAgICAgICAgICBncmFwaHFsRmlsdGVyLnN0YXJ0RGF0ZSA9IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgZ3RlOiBkYXlqcyhmaWx0ZXIuZGF0ZVJhbmdlLnN0YXJ0RGF0ZSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5zdGFydE9mKCdkYXknKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgaWYgKGZpbHRlci5kYXRlUmFuZ2UuZW5kRGF0ZSkge1xyXG4gICAgICAgICAgICAgICAgICAgIGdyYXBocWxGaWx0ZXIuZW5kRGF0ZSA9IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgbHRlOiBkYXlqcyhmaWx0ZXIuZGF0ZVJhbmdlLmVuZERhdGUpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAuZW5kT2YoJ2RheScpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAudG9JU09TdHJpbmcoKSxcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIHZhcmlhYmxlcy5maWx0ZXIgPSBncmFwaHFsRmlsdGVyXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBnZXRJbmNpZGVudFJlY29yZHMoeyB2YXJpYWJsZXMgfSlcclxuICAgIH1cclxuXHJcbiAgICAvLyBIYW5kbGUgZmlsdGVyIGNoYW5nZXNcclxuICAgIGNvbnN0IGhhbmRsZUZpbHRlckNoYW5nZSA9ICh7IHR5cGUsIGRhdGEgfTogYW55KSA9PiB7XHJcbiAgICAgICAgc2V0RmlsdGVyKChwcmV2RmlsdGVyKSA9PiAoe1xyXG4gICAgICAgICAgICAuLi5wcmV2RmlsdGVyLFxyXG4gICAgICAgICAgICBbdHlwZV06IGRhdGEsXHJcbiAgICAgICAgfSkpXHJcbiAgICB9XHJcblxyXG4gICAgLy8gQ3JlYXRlIGNvbHVtbiBkZWZpbml0aW9ucyBmb3IgdGhlIERhdGFUYWJsZVxyXG4gICAgY29uc3QgY29sdW1ucyA9IGNyZWF0ZUNvbHVtbnMoW1xyXG4gICAgICAgIHtcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6ICd0aXRsZScsXHJcbiAgICAgICAgICAgIGhlYWRlcjogKHsgY29sdW1uIH06IHsgY29sdW1uOiBhbnkgfSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgPERhdGFUYWJsZVNvcnRIZWFkZXIgY29sdW1uPXtjb2x1bW59IHRpdGxlPVwiVGl0bGVcIiAvPlxyXG4gICAgICAgICAgICApLFxyXG4gICAgICAgICAgICBjZWxsQ2xhc3NOYW1lOiAndy1mdWxsIHRhYmxldC1sZzp3LWF1dG8nLFxyXG4gICAgICAgICAgICBjZWxsOiAoeyByb3cgfTogeyByb3c6IGFueSB9KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBpbmNpZGVudCA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBzcGFjZS15LTIgbXktMi41XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhyZWY9e2AvaW5jaWRlbnQtcmVjb3Jkcy9lZGl0Lz9pZD0ke2luY2lkZW50LmlkfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaG92ZXI6dGV4dC1jdXJpb3VzLWJsdWUtNDAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2luY2lkZW50LnRpdGxlIHx8ICctJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxQIGNsYXNzTmFtZT1cIiBkZXNrdG9wOmhpZGRlblwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRNZW1iZXJzVG9Ob3RpZnkoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluY2lkZW50Lm1lbWJlcnNUb05vdGlmeSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9QPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gZ2FwLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gbGFwdG9wOmhpZGRlblwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXREYXRlKGluY2lkZW50LnN0YXJ0RGF0ZSl9IC17JyAnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXREYXRlKGluY2lkZW50LmVuZERhdGUpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIE1vYmlsZTogU2hvdyB2ZXNzZWwgYW5kIHJlcG9ydGVkIGJ5ICovfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMi41XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2luY2lkZW50LnZlc3NlbCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRhYmxldC1tZDpoaWRkZW5cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VmVzc2VsTG9jYXRpb25EaXNwbGF5XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbD17Z2V0VmVzc2VsV2l0aEljb24oXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbmNpZGVudC52ZXNzZWwuaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbmNpZGVudC52ZXNzZWwsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbElkPXtpbmNpZGVudC52ZXNzZWwuaWR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXlUZXh0PXtmYWxzZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2VwYXJhdG9yXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb3JpZW50YXRpb249XCJ2ZXJ0aWNhbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC00IHRhYmxldC1tZDpoaWRkZW5cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2luY2lkZW50LnJlcG9ydGVkQnkgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QXZhdGFyIGNsYXNzTmFtZT1cInNpemUtOCB0YWJsZXQtbWQ6aGlkZGVuXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QXZhdGFyRmFsbGJhY2s+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2dldENyZXdJbml0aWFscyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5jaWRlbnQucmVwb3J0ZWRCeS5maXJzdE5hbWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluY2lkZW50LnJlcG9ydGVkQnkuc3VybmFtZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9BdmF0YXJGYWxsYmFjaz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9BdmF0YXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgc29ydGluZ0ZuOiAocm93QTogYW55LCByb3dCOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlQSA9IHJvd0E/Lm9yaWdpbmFsPy50aXRsZSB8fCAnJ1xyXG4gICAgICAgICAgICAgICAgY29uc3QgdmFsdWVCID0gcm93Qj8ub3JpZ2luYWw/LnRpdGxlIHx8ICcnXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWVBLmxvY2FsZUNvbXBhcmUodmFsdWVCKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICBhY2Nlc3NvcktleTogJ21lbWJlcnNUb05vdGlmeScsXHJcbiAgICAgICAgICAgIGhlYWRlcjogJ01lbWJlcnMgdG8gTm90aWZ5JyxcclxuICAgICAgICAgICAgY2VsbEFsaWdubWVudDogJ2xlZnQnIGFzIGNvbnN0LFxyXG4gICAgICAgICAgICBicmVha3BvaW50OiAnZGVza3RvcCcgYXMgY29uc3QsXHJcbiAgICAgICAgICAgIGNlbGxDbGFzc05hbWU6ICdweC0yLjUnLFxyXG4gICAgICAgICAgICBjZWxsOiAoeyByb3cgfTogeyByb3c6IGFueSB9KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBpbmNpZGVudCA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIDw+e2Zvcm1hdE1lbWJlcnNUb05vdGlmeShpbmNpZGVudC5tZW1iZXJzVG9Ob3RpZnkpfTwvPlxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICBhY2Nlc3NvcktleTogJ3Zlc3NlbCcsXHJcbiAgICAgICAgICAgIGhlYWRlcjogKHsgY29sdW1uIH06IHsgY29sdW1uOiBhbnkgfSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgPERhdGFUYWJsZVNvcnRIZWFkZXIgY29sdW1uPXtjb2x1bW59IHRpdGxlPVwiVmVzc2VsXCIgLz5cclxuICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgY2VsbEFsaWdubWVudDogJ2xlZnQnIGFzIGNvbnN0LFxyXG4gICAgICAgICAgICBicmVha3BvaW50OiAndGFibGV0LW1kJyBhcyBjb25zdCxcclxuICAgICAgICAgICAgY2VsbENsYXNzTmFtZTogJ3B4LTIuNScsXHJcbiAgICAgICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogYW55IH0pID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGluY2lkZW50ID0gcm93Lm9yaWdpbmFsXHJcblxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7aW5jaWRlbnQudmVzc2VsICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxWZXNzZWxMb2NhdGlvbkRpc3BsYXlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWw9e2dldFZlc3NlbFdpdGhJY29uKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbmNpZGVudC52ZXNzZWwuaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluY2lkZW50LnZlc3NlbCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbElkPXtpbmNpZGVudC52ZXNzZWwuaWR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheVRleHQ9e2luY2lkZW50LnZlc3NlbC50aXRsZSB8fCAnJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIHNvcnRpbmdGbjogKHJvd0E6IGFueSwgcm93QjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZUEgPSByb3dBPy5vcmlnaW5hbD8udmVzc2VsPy50aXRsZSB8fCAnJ1xyXG4gICAgICAgICAgICAgICAgY29uc3QgdmFsdWVCID0gcm93Qj8ub3JpZ2luYWw/LnZlc3NlbD8udGl0bGUgfHwgJydcclxuICAgICAgICAgICAgICAgIHJldHVybiB2YWx1ZUEubG9jYWxlQ29tcGFyZSh2YWx1ZUIpXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiAnc3RhcnREYXRlJyxcclxuICAgICAgICAgICAgaGVhZGVyOiAoeyBjb2x1bW4gfTogeyBjb2x1bW46IGFueSB9KSA9PiAoXHJcbiAgICAgICAgICAgICAgICA8RGF0YVRhYmxlU29ydEhlYWRlciBjb2x1bW49e2NvbHVtbn0gdGl0bGU9XCJTdGFydCBEYXRlXCIgLz5cclxuICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgY2VsbEFsaWdubWVudDogJ2xlZnQnIGFzIGNvbnN0LFxyXG4gICAgICAgICAgICBicmVha3BvaW50OiAnbGFwdG9wJyBhcyBjb25zdCxcclxuICAgICAgICAgICAgY2VsbENsYXNzTmFtZTogJ3B4LTIuNScsXHJcbiAgICAgICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogYW55IH0pID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGluY2lkZW50ID0gcm93Lm9yaWdpbmFsXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gPD57Zm9ybWF0RGF0ZShpbmNpZGVudC5zdGFydERhdGUpfTwvPlxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBzb3J0aW5nRm46IChyb3dBOiBhbnksIHJvd0I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgdmFsdWVBID0gcm93QT8ub3JpZ2luYWw/LnN0YXJ0RGF0ZSB8fCAnJ1xyXG4gICAgICAgICAgICAgICAgY29uc3QgdmFsdWVCID0gcm93Qj8ub3JpZ2luYWw/LnN0YXJ0RGF0ZSB8fCAnJ1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlQS5sb2NhbGVDb21wYXJlKHZhbHVlQilcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6ICdlbmREYXRlJyxcclxuICAgICAgICAgICAgaGVhZGVyOiAoeyBjb2x1bW4gfTogeyBjb2x1bW46IGFueSB9KSA9PiAoXHJcbiAgICAgICAgICAgICAgICA8RGF0YVRhYmxlU29ydEhlYWRlciBjb2x1bW49e2NvbHVtbn0gdGl0bGU9XCJFbmQgRGF0ZVwiIC8+XHJcbiAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgIGNlbGxBbGlnbm1lbnQ6ICdsZWZ0JyBhcyBjb25zdCxcclxuICAgICAgICAgICAgYnJlYWtwb2ludDogJ2xhcHRvcCcgYXMgY29uc3QsXHJcbiAgICAgICAgICAgIGNlbGxDbGFzc05hbWU6ICdweC0yLjUnLFxyXG4gICAgICAgICAgICBjZWxsOiAoeyByb3cgfTogeyByb3c6IGFueSB9KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBpbmNpZGVudCA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIDw+e2Zvcm1hdERhdGUoaW5jaWRlbnQuZW5kRGF0ZSl9PC8+XHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIHNvcnRpbmdGbjogKHJvd0E6IGFueSwgcm93QjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZUEgPSByb3dBPy5vcmlnaW5hbD8uZW5kRGF0ZSB8fCAnJ1xyXG4gICAgICAgICAgICAgICAgY29uc3QgdmFsdWVCID0gcm93Qj8ub3JpZ2luYWw/LmVuZERhdGUgfHwgJydcclxuICAgICAgICAgICAgICAgIHJldHVybiB2YWx1ZUEubG9jYWxlQ29tcGFyZSh2YWx1ZUIpXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiAncmVwb3J0ZWRCeScsXHJcbiAgICAgICAgICAgIGhlYWRlcjogKHsgY29sdW1uIH06IHsgY29sdW1uOiBhbnkgfSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgPERhdGFUYWJsZVNvcnRIZWFkZXIgY29sdW1uPXtjb2x1bW59IHRpdGxlPVwiUmVwb3J0ZWQgQnlcIiAvPlxyXG4gICAgICAgICAgICApLFxyXG4gICAgICAgICAgICBicmVha3BvaW50OiAndGFibGV0LW1kJyBhcyBjb25zdCxcclxuICAgICAgICAgICAgY2VsbEFsaWdubWVudDogJ2xlZnQnIGFzIGNvbnN0LFxyXG4gICAgICAgICAgICBjZWxsQ2xhc3NOYW1lOiAncHgtMi41JyxcclxuICAgICAgICAgICAgY2VsbDogKHsgcm93IH06IHsgcm93OiBhbnkgfSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgaW5jaWRlbnQgPSByb3cub3JpZ2luYWxcclxuICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICAgICAge2luY2lkZW50LnJlcG9ydGVkQnkgJiYgaW5jaWRlbnQucmVwb3J0ZWRCeS5pZCA+IDAgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMi41XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRvb2x0aXAgbW9iaWxlQ2xpY2thYmxlPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VG9vbHRpcFRyaWdnZXIgbW9iaWxlQ2xpY2thYmxlPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEF2YXRhciBjbGFzc05hbWU9XCJoLTggdy04XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEF2YXRhckZhbGxiYWNrIGNsYXNzTmFtZT1cInRleHQteHNcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2dldENyZXdJbml0aWFscyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluY2lkZW50LnJlcG9ydGVkQnlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuZmlyc3ROYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5jaWRlbnQucmVwb3J0ZWRCeS5zdXJuYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQXZhdGFyRmFsbGJhY2s+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0F2YXRhcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwVHJpZ2dlcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRvb2x0aXBDb250ZW50IGNsYXNzTmFtZT1cImxnOmhpZGRlblwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdE5hbWUoaW5jaWRlbnQucmVwb3J0ZWRCeSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVG9vbHRpcENvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImhpZGRlbiBsZzpibG9ja1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0TmFtZShpbmNpZGVudC5yZXBvcnRlZEJ5KX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgc29ydGluZ0ZuOiAocm93QTogYW55LCByb3dCOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlQSA9IGZvcm1hdE5hbWUocm93QT8ub3JpZ2luYWw/LnJlcG9ydGVkQnkpIHx8ICcnXHJcbiAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZUIgPSBmb3JtYXROYW1lKHJvd0I/Lm9yaWdpbmFsPy5yZXBvcnRlZEJ5KSB8fCAnJ1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlQS5sb2NhbGVDb21wYXJlKHZhbHVlQilcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgXSlcclxuXHJcbiAgICByZXR1cm4gKFxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIHB5LTBcIj5cclxuICAgICAgICAgICAgPExpc3RIZWFkZXJcclxuICAgICAgICAgICAgICAgIGljb249ezxTZWFsb2dzSW5jaWRlbnRJY29uIGNsYXNzTmFtZT1cInctOCBoLThcIiAvPn1cclxuICAgICAgICAgICAgICAgIHRpdGxlPVwiSW5jaWRlbnQgUmVjb3Jkc1wiXHJcbiAgICAgICAgICAgICAgICBhY3Rpb25zPXtcclxuICAgICAgICAgICAgICAgICAgICA8RHJvcGRvd25NZW51PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8RHJvcGRvd25NZW51VHJpZ2dlciBhc0NoaWxkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlYWxvZ3NDb2dJY29uIHNpemU9ezM2fSAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0Ryb3Bkb3duTWVudVRyaWdnZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPERyb3Bkb3duTWVudUl0ZW1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJvdXRlci5wdXNoKCcvaW5jaWRlbnQtcmVjb3Jkcy9jcmVhdGUnKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIE5ldyBpbmNpZGVudCByZWNvcmRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51SXRlbT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51PlxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAvPlxyXG5cclxuICAgICAgICAgICAgey8qIENoYXJ0IERhc2hib2FyZCAqL31cclxuICAgICAgICAgICAgPEluY2lkZW50Q2hhcnRzRGFzaGJvYXJkIC8+XHJcblxyXG4gICAgICAgICAgICB7LyogVGFibGUgd2l0aCBpbnRlZ3JhdGVkIGZpbHRlcnMgKi99XHJcbiAgICAgICAgICAgIDxEYXRhVGFibGVcclxuICAgICAgICAgICAgICAgIGNvbHVtbnM9e2NvbHVtbnN9XHJcbiAgICAgICAgICAgICAgICBkYXRhPXtpbmNpZGVudFJlY29yZHN9XHJcbiAgICAgICAgICAgICAgICBub0RhdGFUZXh0PVwiTm8gaW5jaWRlbnQgcmVjb3JkcyBmb3VuZFwiXHJcbiAgICAgICAgICAgICAgICBzaG93VG9vbGJhcj17dHJ1ZX1cclxuICAgICAgICAgICAgICAgIGlzTG9hZGluZz17aXNMb2FkaW5nIHx8IHF1ZXJ5SW5jaWRlbnRSZWNvcmRzTG9hZGluZ31cclxuICAgICAgICAgICAgICAgIHBhZ2VTaXplPXsxMH1cclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVGaWx0ZXJDaGFuZ2V9XHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICApXHJcbn1cclxuZXhwb3J0IGRlZmF1bHQgSW5jaWRlbnRSZWNvcmRMaXN0XHJcbiJdLCJuYW1lcyI6WyJ1c2VSb3V0ZXIiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsInVzZUxhenlRdWVyeSIsIkdFVF9JTkNJREVOVF9SRUNPUkRTIiwiZGF5anMiLCJmb3JtYXREYXRlIiwiTGluayIsIkRhdGFUYWJsZSIsImNyZWF0ZUNvbHVtbnMiLCJEYXRhVGFibGVTb3J0SGVhZGVyIiwiTGlzdEhlYWRlciIsIkF2YXRhciIsIkF2YXRhckZhbGxiYWNrIiwiVmVzc2VsTG9jYXRpb25EaXNwbGF5IiwiU2VhbG9nc0luY2lkZW50SWNvbiIsIkluY2lkZW50Q2hhcnRzRGFzaGJvYXJkIiwidXNlVmVzc2VsSWNvbkRhdGEiLCJEcm9wZG93bk1lbnUiLCJEcm9wZG93bk1lbnVDb250ZW50IiwiRHJvcGRvd25NZW51VHJpZ2dlciIsIlAiLCJTZXBhcmF0b3IiLCJUb29sdGlwIiwiVG9vbHRpcENvbnRlbnQiLCJUb29sdGlwVHJpZ2dlciIsIlNlYWxvZ3NDb2dJY29uIiwiRHJvcGRvd25NZW51SXRlbSIsImdldENyZXdJbml0aWFscyIsImZpcnN0TmFtZSIsInN1cm5hbWUiLCJmaXJzdCIsImNoYXJBdCIsInRvVXBwZXJDYXNlIiwibGFzdCIsImZvcm1hdE5hbWUiLCJwZXJzb24iLCJpZCIsImZvcm1hdE1lbWJlcnNUb05vdGlmeSIsIm1lbWJlcnMiLCJub2RlcyIsImxlbmd0aCIsIm1hcCIsIm1lbWJlciIsImpvaW4iLCJJbmNpZGVudFJlY29yZExpc3QiLCJyb3V0ZXIiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJpbmNpZGVudFJlY29yZHMiLCJzZXRJbmNpZGVudFJlY29yZHMiLCJmaWx0ZXIiLCJzZXRGaWx0ZXIiLCJnZXRWZXNzZWxXaXRoSWNvbiIsImdldEluY2lkZW50UmVjb3JkcyIsImxvYWRpbmciLCJxdWVyeUluY2lkZW50UmVjb3Jkc0xvYWRpbmciLCJmZXRjaFBvbGljeSIsIm9uQ29tcGxldGVkIiwicmVzcG9uc2UiLCJyZWFkSW5jaWRlbnRSZWNvcmRzIiwib25FcnJvciIsImVycm9yIiwiY29uc29sZSIsImxvYWRJbmNpZGVudFJlY29yZHMiLCJ2YXJpYWJsZXMiLCJsaW1pdCIsIk9iamVjdCIsImtleXMiLCJncmFwaHFsRmlsdGVyIiwidmVzc2VsIiwidmFsdWUiLCJ2ZXNzZWxJRCIsImVxIiwiZGF0ZVJhbmdlIiwic3RhcnREYXRlIiwiZ3RlIiwic3RhcnRPZiIsInRvSVNPU3RyaW5nIiwiZW5kRGF0ZSIsImx0ZSIsImVuZE9mIiwiaGFuZGxlRmlsdGVyQ2hhbmdlIiwidHlwZSIsImRhdGEiLCJwcmV2RmlsdGVyIiwiY29sdW1ucyIsImFjY2Vzc29yS2V5IiwiaGVhZGVyIiwiY29sdW1uIiwidGl0bGUiLCJjZWxsQ2xhc3NOYW1lIiwiY2VsbCIsInJvdyIsImluY2lkZW50Iiwib3JpZ2luYWwiLCJkaXYiLCJjbGFzc05hbWUiLCJocmVmIiwibWVtYmVyc1RvTm90aWZ5IiwicCIsInZlc3NlbElkIiwiZGlzcGxheVRleHQiLCJvcmllbnRhdGlvbiIsInJlcG9ydGVkQnkiLCJzb3J0aW5nRm4iLCJyb3dBIiwicm93QiIsInZhbHVlQSIsInZhbHVlQiIsImxvY2FsZUNvbXBhcmUiLCJjZWxsQWxpZ25tZW50IiwiYnJlYWtwb2ludCIsIm1vYmlsZUNsaWNrYWJsZSIsInNwYW4iLCJpY29uIiwiYWN0aW9ucyIsImFzQ2hpbGQiLCJzaXplIiwib25DbGljayIsInB1c2giLCJub0RhdGFUZXh0Iiwic2hvd1Rvb2xiYXIiLCJwYWdlU2l6ZSIsIm9uQ2hhbmdlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/incident-record/incident-record-list.tsx\n"));

/***/ })

});