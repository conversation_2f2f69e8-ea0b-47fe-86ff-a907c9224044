"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/list/list.tsx":
/*!***************************************!*\
  !*** ./src/app/ui/crew/list/list.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CrewTable: function() { return /* binding */ CrewTable; },\n/* harmony export */   \"default\": function() { return /* binding */ CrewList; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _barrel_optimize_names_Check_CircleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CircleAlert!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_CircleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CircleAlert!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _app_lib_icons_SealogsCrewIcon__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/lib/icons/SealogsCrewIcon */ \"(app-pages-browser)/./src/app/lib/icons/SealogsCrewIcon.ts\");\n/* harmony import */ var _components_filter_components_crew_actions__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/filter/components/crew-actions */ \"(app-pages-browser)/./src/components/filter/components/crew-actions.tsx\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/crew/list/queries.ts\");\n/* harmony import */ var _components_vessel_display__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../components/vessel-display */ \"(app-pages-browser)/./src/app/ui/crew/components/vessel-display.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,CrewTable auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CrewList(props) {\n    _s();\n    const [crewList, setCrewList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showActiveUsers, setShowActiveUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { isMobile } = (0,_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.useSidebar)();\n    // const [isLoading, setIsLoading] = useState(true)\n    const [departments, setDepartments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [duties, setDuties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const limit = 100;\n    const [pageInfo, setPageInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalCount: 0,\n        hasNextPage: false,\n        hasPreviousPage: false\n    });\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    let [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isArchived: {\n            eq: false\n        }\n    });\n    const [trainingStatusFilter, setTrainingStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Update crew duties based on active/archived state.\n    const handleSetCrewDuties = (crewDuties)=>{\n        const activeDuties = crewDuties.filter((duty)=>showActiveUsers ? !duty.archived : duty.archived);\n        const formattedCrewDuties = activeDuties.map((duty)=>{\n            return {\n                label: duty.title,\n                value: duty.id\n            };\n        });\n        setDuties(formattedCrewDuties);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_4__.getCrewDuties)(handleSetCrewDuties);\n    // Render departments recursively.\n    const renderDepartment = function(departments) {\n        let parentID = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0, depth = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;\n        return departments.filter((department)=>+department.parentID === parentID).flatMap((department)=>{\n            const children = renderDepartment(departments, +department.id, depth + 1);\n            const item = {\n                ...department,\n                level: depth\n            };\n            return [\n                item,\n                ...children\n            ];\n        });\n    };\n    /* const [readDepartments, { loading: readDepartmentsLoading }] = useLazyQuery(\r\n        ReadDepartments,\r\n        {\r\n            fetchPolicy: 'cache-and-network',\r\n            onCompleted: (response: any) => {\r\n                const data = response.readDepartments.nodes\r\n                if (data) {\r\n                    const formattedData = renderDepartment(data)\r\n                    setDepartments(formattedData)\r\n                }\r\n            },\r\n            onError: (error: any) => {\r\n                console.error('queryCrewMembers error', error)\r\n            },\r\n        },\r\n    ) */ /* const loadDepartments = async () => {\r\n        await readDepartments()\r\n    } */ /* useEffect(() => {\r\n        if (isLoading) {\r\n            loadDepartments()\r\n            setIsLoading(false)\r\n        }\r\n    }, [isLoading]) */ // Set vessels from vessel brief list.\n    const handleSetVessels = (vessels)=>{\n        const vesselSelection = vessels.map((vessel)=>{\n            return {\n                label: vessel.title,\n                value: vessel.id\n            };\n        });\n        setVessels(vesselSelection);\n    //loadCrewMembers()\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_4__.getVesselBriefList)(handleSetVessels);\n    const [queryCrewMembers, { loading: queryCrewMembersLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_15__.ReadSeaLogsMembers, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            handleSetCrewMembers(response.readSeaLogsMembers.nodes);\n            setPageInfo(response.readSeaLogsMembers.pageInfo);\n            return response.readSeaLogsMembers.nodes;\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewMembers error\", error);\n        }\n    });\n    const handleSetCrewMembers = (crewMembers)=>{\n        const transformedCrewList = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_4__.GetCrewListWithTrainingStatus)(crewMembers, vessels);\n        setCrewList(transformedCrewList);\n    };\n    // Function to filter crew list by training status\n    const filterCrewByTrainingStatus = (crewList, statusFilter)=>{\n        const crews = [\n            ...crewList\n        ].map((crew)=>{\n            const dues = crew.trainingStatus.dues;\n            if (dues.length === 0) {\n                return {\n                    ...crew,\n                    trainingStatus: {\n                        label: \"Good\",\n                        dues: []\n                    }\n                };\n            } else if (dues.some((due)=>due.status.isOverdue)) {\n                return {\n                    ...crew,\n                    trainingStatus: {\n                        label: \"Overdue\",\n                        dues: dues.filter((due)=>due.status.isOverdue)\n                    }\n                };\n            } else {\n                return {\n                    ...crew,\n                    trainingStatus: {\n                        label: \" \",\n                        dues: dues.filter((due)=>!due.status.isOverdue)\n                    }\n                };\n            }\n        });\n        if (!statusFilter) return crews;\n        return crews.filter((crew)=>{\n            var _crew_trainingStatus, _crew_trainingStatus1;\n            const trainingStatus = (_crew_trainingStatus = crew.trainingStatus) === null || _crew_trainingStatus === void 0 ? void 0 : _crew_trainingStatus.label;\n            const dues = ((_crew_trainingStatus1 = crew.trainingStatus) === null || _crew_trainingStatus1 === void 0 ? void 0 : _crew_trainingStatus1.dues) || [];\n            if (statusFilter === \"Good\") {\n                return trainingStatus === \"Good\";\n            } else if (statusFilter === \"Overdue\") {\n                return trainingStatus === \"Overdue\";\n            } else if (statusFilter === \"Due Soon\") {\n                // Due Soon is represented by an empty string label with dues\n                // This happens when there are training sessions due within 7 days but not overdue\n                return trainingStatus === \" \" && dues.length > 0;\n            }\n            return true;\n        });\n    };\n    // Get filtered crew list for display\n    const filteredCrewList = filterCrewByTrainingStatus(crewList, trainingStatusFilter);\n    const loadCrewMembers = async function() {\n        let startPage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, searchFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {\n            ...filter\n        };\n        /*searchFilter.isArchived = { eq: !showActiveUsers }\r\n        const updatedFilter: SearchFilter = {\r\n            ...searchFilter,\r\n            isArchived: { eq: !showActiveUsers },\r\n        }*/ await queryCrewMembers({\n            variables: {\n                limit: limit,\n                offset: startPage * limit,\n                filter: searchFilter\n            }\n        });\n    };\n    const [mutationUpdateUser] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UPDATE_USER, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"mutationUpdateUser error\", error);\n        }\n    });\n    const handleCrewDuty = async (duty, user)=>{\n        const selectedUser = {\n            ...crewList.find((crew)=>crew.ID === user.ID)\n        };\n        const newPrimaryDutyID = duty.value;\n        if (selectedUser) {\n            const updatedCrewList = crewList.map((crew)=>{\n                if (crew.ID === user.ID) {\n                    return {\n                        ...crew,\n                        PrimaryDutyID: newPrimaryDutyID\n                    };\n                }\n                return crew;\n            });\n            setCrewList(updatedCrewList);\n            // Update user\n            const variables = {\n                input: {\n                    id: +user.id,\n                    primaryDutyID: newPrimaryDutyID\n                }\n            };\n            await mutationUpdateUser({\n                variables\n            });\n        }\n    };\n    const handleNavigationClick = (newPage)=>{\n        if (newPage < 0 || newPage === page) return;\n        setPage(newPage);\n        loadCrewMembers(newPage);\n    };\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        const searchFilter = {\n            ...filter\n        };\n        // Handle training status filter separately since it's client-side\n        if (type === \"trainingStatus\") {\n            if (data && data.value) {\n                setTrainingStatusFilter(data.value);\n            } else {\n                setTrainingStatusFilter(null);\n            }\n            return; // Don't reload crew members for client-side filter\n        }\n        if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.vehicles = {\n                    id: {\n                        in: data.map((item)=>+item.value)\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.vehicles = {\n                    id: {\n                        contains: +data.value\n                    }\n                };\n            } else {\n                delete searchFilter.vehicles;\n            }\n        }\n        if (type === \"crewDuty\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.primaryDutyID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.primaryDutyID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.primaryDutyID;\n            }\n        }\n        if (type === \"keyword\") {\n            if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(data.value)) {\n                searchFilter.q = {\n                    contains: data.value\n                };\n            } else {\n                delete searchFilter.q;\n            }\n        }\n        if (type === \"isArchived\") {\n            if (data !== undefined) {\n                searchFilter.isArchived = {\n                    eq: !data\n                };\n            } else {\n                delete searchFilter.isArchived;\n            }\n        }\n        setFilter(searchFilter);\n        //setPage(0)\n        loadCrewMembers(0, searchFilter);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPage(0);\n        loadCrewMembers(0, filter);\n    }, [\n        showActiveUsers,\n        filter\n    ]);\n    // Column definitions for the DataTable.\n    const columns = [\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Name\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 17\n                }, this);\n            },\n            cellClassName: \"phablet:w-auto w-full\",\n            cell: (param)=>{\n                let { row } = param;\n                var _crewMember_trainingStatus;\n                const crewMember = row.original;\n                const fullName = \"\".concat(crewMember.firstName, \" \").concat(crewMember.surname);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full py-2.5 space-y-1.5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 flex justify-start items-center gap-2.5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Avatar, {\n                                            size: \"sm\",\n                                            variant: ((_crewMember_trainingStatus = crewMember.trainingStatus) === null || _crewMember_trainingStatus === void 0 ? void 0 : _crewMember_trainingStatus.label) === \"Overdue\" ? \"destructive\" : \"success\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.AvatarFallback, {\n                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_10__.getCrewInitials)(crewMember.firstName, crewMember.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 37\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid min-w-32\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/crew/info?id=\".concat(crewMember.id),\n                                                    className: \"items-center truncate text-nowrap\",\n                                                    children: fullName || \"--\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-curious-blue-400 uppercase laptop:hidden text-[10px]\",\n                                                    children: crewMember.primaryDuty.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"phablet:hidden\",\n                                    children: crewMember.trainingStatus.label === \"Overdue\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                        variant: \"destructive\",\n                                        type: \"circle\",\n                                        children: crewMember.trainingStatus.dues.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 37\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                        variant: \"success\",\n                                        type: \"circle\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CircleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 37\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 25\n                        }, this),\n                        crewMember.vehicles.nodes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"tablet-lg:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_vessel_display__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                vessels: crewMember.vehicles.nodes,\n                                maxVisibleVessels: 3\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 33\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 29\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                    lineNumber: 342,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowA_original1, _rowB_original, _rowB_original1;\n                const fullNameA = \"\".concat(rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.firstName, \" \").concat(rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : _rowA_original1.surname) || \"\";\n                const fullNameB = \"\".concat(rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.firstName, \" \").concat(rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : _rowB_original1.surname) || \"\";\n                return fullNameA.localeCompare(fullNameB);\n            }\n        },\n        {\n            accessorKey: \"vehicles\",\n            cellAlignment: \"left\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Vessel\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                    lineNumber: 410,\n                    columnNumber: 17\n                }, this);\n            },\n            breakpoint: \"tablet-lg\",\n            cell: (param)=>{\n                let { row } = param;\n                const crew = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_vessel_display__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    vessels: crew.vehicles.nodes,\n                    maxVisibleVessels: 4\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                    lineNumber: 416,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_vehicles_nodes_, _rowA_original_vehicles_nodes, _rowA_original_vehicles, _rowA_original, _rowB_original_vehicles_nodes_, _rowB_original_vehicles_nodes, _rowB_original_vehicles, _rowB_original;\n                const titleA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vehicles = _rowA_original.vehicles) === null || _rowA_original_vehicles === void 0 ? void 0 : (_rowA_original_vehicles_nodes = _rowA_original_vehicles.nodes) === null || _rowA_original_vehicles_nodes === void 0 ? void 0 : (_rowA_original_vehicles_nodes_ = _rowA_original_vehicles_nodes[0]) === null || _rowA_original_vehicles_nodes_ === void 0 ? void 0 : _rowA_original_vehicles_nodes_.title) || \"\";\n                const titleB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vehicles = _rowB_original.vehicles) === null || _rowB_original_vehicles === void 0 ? void 0 : (_rowB_original_vehicles_nodes = _rowB_original_vehicles.nodes) === null || _rowB_original_vehicles_nodes === void 0 ? void 0 : (_rowB_original_vehicles_nodes_ = _rowB_original_vehicles_nodes[0]) === null || _rowB_original_vehicles_nodes_ === void 0 ? void 0 : _rowB_original_vehicles_nodes_.title) || \"\";\n                return titleA.localeCompare(titleB);\n            }\n        },\n        {\n            accessorKey: \"primaryDuty\",\n            cellAlignment: \"right\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Primary duty\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                    lineNumber: 433,\n                    columnNumber: 17\n                }, this);\n            },\n            breakpoint: \"laptop\",\n            cell: (param)=>{\n                let { row } = param;\n                const crew = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"whitespace-normal px-5\",\n                    children: crew.primaryDuty.title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                    lineNumber: 439,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_primaryDuty, _rowA_original, _rowB_original_primaryDuty, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_primaryDuty = _rowA_original.primaryDuty) === null || _rowA_original_primaryDuty === void 0 ? void 0 : _rowA_original_primaryDuty.title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_primaryDuty = _rowB_original.primaryDuty) === null || _rowB_original_primaryDuty === void 0 ? void 0 : _rowB_original_primaryDuty.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"trainingStatus\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Training\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                        lineNumber: 454,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                    lineNumber: 453,\n                    columnNumber: 17\n                }, this);\n            },\n            breakpoint: \"phablet\",\n            cell: (param)=>{\n                let { row } = param;\n                const crew = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center w-full\",\n                    children: crew.trainingStatus.label === \"Overdue\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                        variant: \"destructive\",\n                        type: \"circle\",\n                        children: crew.trainingStatus.dues.length\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                        lineNumber: 463,\n                        columnNumber: 29\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                        variant: \"success\",\n                        type: \"circle\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CircleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                        lineNumber: 467,\n                        columnNumber: 29\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                    lineNumber: 461,\n                    columnNumber: 21\n                }, this);\n            }\n        }\n    ];\n    const handleDropdownChange = (type, data)=>{\n        handleFilterOnChange({\n            type,\n            data\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_14__.ListHeader, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons_SealogsCrewIcon__WEBPACK_IMPORTED_MODULE_12__.SealogsCrewIcon, {\n                    className: \"size-12\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                    lineNumber: 485,\n                    columnNumber: 21\n                }, void 0),\n                title: \"All crew\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_crew_actions__WEBPACK_IMPORTED_MODULE_13__.CrewFilterActions, {\n                    onChange: (data)=>{\n                        handleDropdownChange(\"isArchived\", data);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                    lineNumber: 491,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                lineNumber: 483,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                    isLoading: queryCrewMembersLoading,\n                    columns: columns,\n                    data: filteredCrewList,\n                    pageSize: 20,\n                    onChange: handleFilterOnChange\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                    lineNumber: 499,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                lineNumber: 498,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(CrewList, \"+OOaMI7N+9QMtTfwtWp3GfR71zw=\", false, function() {\n    return [\n        _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.useSidebar,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation\n    ];\n});\n_c = CrewList;\n// ---------------------------------------------------------------------------------------//\nconst CrewTable = (param)=>{\n    let { crewList, vessels, handleCrewDuty = false, showSurname } = param;\n    _s1();\n    const [isAdmin, setIsAdmin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [departments, setDepartments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const renderDepartment = function(departments) {\n        let parentID = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0, depth = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;\n        return departments.filter((department)=>+department.parentID === parentID).flatMap((department)=>{\n            const children = renderDepartment(departments, +department.id, depth + 1);\n            const item = {\n                ...department,\n                level: depth\n            };\n            return [\n                item,\n                ...children\n            ];\n        });\n    };\n    const [readDepartments, { loading: readDepartmentsLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_15__.ReadDepartments, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readDepartments.nodes;\n            if (data) {\n                const formattedData = renderDepartment(data);\n                setDepartments(formattedData);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewMembers error\", error);\n        }\n    });\n    const loadDepartments = async ()=>{\n        await readDepartments();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadDepartments();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const crewListWithTrainingStatus = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_4__.GetCrewListWithTrainingStatus)(crewList, vessels);\n    const transformedCrewList = crewListWithTrainingStatus.map((crewMember)=>{\n        const filteredDues = crewMember.trainingStatus.dues.filter((due)=>{\n            return crewMember.vehicles.nodes.some((node)=>node.id === due.vesselID);\n        });\n        const updatedTrainingStatus = {\n            ...crewMember.trainingStatus,\n            dues: filteredDues\n        };\n        if (filteredDues.length === 0) {\n            updatedTrainingStatus.label = \"Good\";\n        }\n        return {\n            ...crewMember,\n            trainingStatus: updatedTrainingStatus\n        };\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if ( true && typeof window.localStorage !== \"undefined\") {\n            const result = localStorage.getItem(\"admin\");\n            const admin = result === \"true\";\n            setIsAdmin(admin);\n        }\n    }, []);\n    const columns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: \"\",\n            cell: (param)=>{\n                let { row } = param;\n                var _crewMember_trainingStatus;\n                const crewMember = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2.5 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Avatar, {\n                                    size: \"sm\",\n                                    variant: ((_crewMember_trainingStatus = crewMember.trainingStatus) === null || _crewMember_trainingStatus === void 0 ? void 0 : _crewMember_trainingStatus.label) !== \"Good\" ? \"destructive\" : \"success\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.AvatarFallback, {\n                                        children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_10__.getCrewInitials)(crewMember.firstName, crewMember.surname)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                        lineNumber: 616,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                    lineNumber: 609,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/crew/info?id=\".concat(crewMember.id),\n                                    className: \"flex items-center pl-2 text-nowrap\",\n                                    children: [\n                                        crewMember.firstName || \"--\",\n                                        showSurname == true ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"\\xa0\",\n                                                crewMember.surname || \"--\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                            lineNumber: 628,\n                                            columnNumber: 37\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden md:flex\",\n                                            children: [\n                                                \"\\xa0\",\n                                                crewMember.surname || \"--\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                            lineNumber: 633,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                    lineNumber: 623,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                            lineNumber: 608,\n                            columnNumber: 25\n                        }, undefined),\n                        handleCrewDuty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex md:hidden flex-col\",\n                            children: crewMember.vehicles.nodes && crewMember.vehicles.nodes.map((vessel, index)=>{\n                                if (index < 2) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-muted font-light rounded-lg p-2 border m-1 border-border text-nowrap\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"max-w-32 overflow-hidden block\",\n                                            href: \"/vessel/info?id=\".concat(vessel.id),\n                                            children: vessel.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                            lineNumber: 651,\n                                            columnNumber: 57\n                                        }, undefined)\n                                    }, vessel.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 53\n                                    }, undefined);\n                                }\n                                if (index === 2) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_11__.Popover, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_11__.PopoverTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    className: \"text-orange-500\",\n                                                    children: [\n                                                        \"+\",\n                                                        \" \",\n                                                        crewMember.vehicles.nodes.length - 2,\n                                                        \" \",\n                                                        \"more\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                                    lineNumber: 663,\n                                                    columnNumber: 61\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                                lineNumber: 662,\n                                                columnNumber: 57\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_11__.PopoverContent, {\n                                                className: \"p-0 w-64\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-h-full bg-background rounded\",\n                                                    children: crewMember.vehicles.nodes.slice(2).map((v)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex cursor-pointer hover:bg-muted items-center overflow-auto px-3 py-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    href: \"/vessel/info?id=\".concat(v.id),\n                                                                    children: v.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                                                    lineNumber: 690,\n                                                                    columnNumber: 85\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                                                lineNumber: 689,\n                                                                columnNumber: 81\n                                                            }, undefined)\n                                                        }, v.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                                            lineNumber: 684,\n                                                            columnNumber: 77\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                                    lineNumber: 677,\n                                                    columnNumber: 61\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                                lineNumber: 676,\n                                                columnNumber: 57\n                                            }, undefined)\n                                        ]\n                                    }, vessel.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                        lineNumber: 661,\n                                        columnNumber: 53\n                                    }, undefined);\n                                }\n                                return null;\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                            lineNumber: 642,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                    lineNumber: 607,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"vessel\",\n            header: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: handleCrewDuty && \"Vessel\"\n                }, void 0, false),\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const crew = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: handleCrewDuty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: crew.vehicles.nodes && crew.vehicles.nodes.map((vessel, index)=>{\n                            if (index < 2) {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-muted inline-block font-light rounded-lg p-2 border border-border m-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/vessel/info?id=\".concat(vessel.id),\n                                        children: vessel.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                        lineNumber: 732,\n                                        columnNumber: 57\n                                    }, undefined)\n                                }, vessel.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                    lineNumber: 729,\n                                    columnNumber: 53\n                                }, undefined);\n                            }\n                            if (index === 2) {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_11__.Popover, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_11__.PopoverTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                className: \"text-orange-500\",\n                                                children: [\n                                                    \"+\",\n                                                    \" \",\n                                                    crew.vehicles.nodes.length - 2,\n                                                    \" \",\n                                                    \"more\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                                lineNumber: 743,\n                                                columnNumber: 61\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                            lineNumber: 742,\n                                            columnNumber: 57\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_11__.PopoverContent, {\n                                            className: \"p-0 w-64\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-h-full bg-background rounded\",\n                                                children: crew.vehicles.nodes.slice(2).map((v)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex cursor-pointer hover:bg-muted items-center overflow-auto px-3 py-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                href: \"/vessel/info?id=\".concat(v.id),\n                                                                children: v.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                                                lineNumber: 769,\n                                                                columnNumber: 85\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                                            lineNumber: 768,\n                                                            columnNumber: 81\n                                                        }, undefined)\n                                                    }, v.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                                        lineNumber: 763,\n                                                        columnNumber: 77\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                                lineNumber: 756,\n                                                columnNumber: 61\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                            lineNumber: 755,\n                                            columnNumber: 57\n                                        }, undefined)\n                                    ]\n                                }, vessel.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                    lineNumber: 741,\n                                    columnNumber: 53\n                                }, undefined);\n                            }\n                            return null;\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                        lineNumber: 723,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            }\n        },\n        {\n            accessorKey: \"primaryDuty\",\n            header: \"Primary Duty\",\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const crew = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-wrap text-right whitespace-normal\",\n                    children: crew.primaryDuty.title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                    lineNumber: 800,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"trainingStatus\",\n            header: \"Training status\",\n            cell: (param)=>{\n                let { row } = param;\n                const crew = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center\",\n                    children: crew.trainingStatus.label !== \"Good\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_11__.Popover, {\n                        triggerType: \"hover\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_11__.PopoverTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CircleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    strokeWidth: 1,\n                                    className: \"h-9 w-9 text-destructive cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                    lineNumber: 817,\n                                    columnNumber: 37\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                lineNumber: 816,\n                                columnNumber: 33\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_11__.PopoverContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-background rounded p-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs whitespace-nowrap font-medium focus:outline-none inline-block rounded\",\n                                        children: crew.trainingStatus.dues.map((item, dueIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"\".concat(item.trainingType.title, \" - \").concat(item.status.label)\n                                            }, dueIndex, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                                lineNumber: 830,\n                                                columnNumber: 53\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                        lineNumber: 824,\n                                        columnNumber: 41\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                    lineNumber: 823,\n                                    columnNumber: 37\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                lineNumber: 822,\n                                columnNumber: 33\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                        lineNumber: 815,\n                        columnNumber: 29\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                        variant: \"success\",\n                        type: \"circle\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CircleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                            lineNumber: 841,\n                            columnNumber: 33\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                        lineNumber: 840,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                    lineNumber: 813,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"departments\",\n            header: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: isAdmin && localStorage.getItem(\"useDepartment\") === \"true\" && \"Departments\"\n                }, void 0, false),\n            cell: (param)=>{\n                let { row } = param;\n                const crew = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: isAdmin && localStorage.getItem(\"useDepartment\") === \"true\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: crew.departments && crew.departments.nodes.length > 0 ? crew.departments.nodes.map((department)=>{\n                            var _departments_find;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/department/info?id=\".concat(department.id),\n                                className: \"flex flex-col text-nowrap\",\n                                children: (_departments_find = departments.find((dept)=>dept.id === department.id)) === null || _departments_find === void 0 ? void 0 : _departments_find.title\n                            }, department.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                                lineNumber: 869,\n                                columnNumber: 49\n                            }, undefined);\n                        }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"No departments found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                            lineNumber: 884,\n                            columnNumber: 41\n                        }, undefined)\n                    }, void 0, false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n                    lineNumber: 860,\n                    columnNumber: 21\n                }, undefined);\n            }\n        }\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n        columns: columns,\n        showToolbar: false,\n        data: transformedCrewList,\n        pageSize: 20\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list\\\\list.tsx\",\n        lineNumber: 895,\n        columnNumber: 9\n    }, undefined);\n};\n_s1(CrewTable, \"5wAh4WvsScd0iqV5WA++PbFH7ak=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery\n    ];\n});\n_c1 = CrewTable;\nvar _c, _c1;\n$RefreshReg$(_c, \"CrewList\");\n$RefreshReg$(_c1, \"CrewTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/list/list.tsx\n"));

/***/ })

});