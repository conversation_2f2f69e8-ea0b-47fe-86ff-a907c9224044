"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/incident-records/page",{

/***/ "(app-pages-browser)/./src/app/ui/incident-record/incident-record-list.tsx":
/*!*************************************************************!*\
  !*** ./src/app/ui/incident-record/incident-record-list.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _graphql_queries__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./graphql/queries */ \"(app-pages-browser)/./src/app/ui/incident-record/graphql/queries.ts\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/vessel-location-display */ \"(app-pages-browser)/./src/components/ui/vessel-location-display.tsx\");\n/* harmony import */ var _app_lib_icons_SealogsIncidentIcon__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/lib/icons/SealogsIncidentIcon */ \"(app-pages-browser)/./src/app/lib/icons/SealogsIncidentIcon.ts\");\n/* harmony import */ var _components_incident_charts_dashboard__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./components/incident-charts-dashboard */ \"(app-pages-browser)/./src/app/ui/incident-record/components/incident-charts-dashboard.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _app_lib_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/lib/icons */ \"(app-pages-browser)/./src/app/lib/icons/index.ts\");\n/* harmony import */ var _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @radix-ui/react-dropdown-menu */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-dropdown-me_8ec93851da28661231dcc925c4c21d7f/node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper functions\nconst getCrewInitials = (firstName, surname)=>{\n    var _firstName_charAt, _surname_charAt;\n    if (!firstName && !surname) return \"??\";\n    const first = (firstName === null || firstName === void 0 ? void 0 : (_firstName_charAt = firstName.charAt(0)) === null || _firstName_charAt === void 0 ? void 0 : _firstName_charAt.toUpperCase()) || \"\";\n    const last = (surname === null || surname === void 0 ? void 0 : (_surname_charAt = surname.charAt(0)) === null || _surname_charAt === void 0 ? void 0 : _surname_charAt.toUpperCase()) || \"\";\n    return \"\".concat(first).concat(last) || \"??\";\n};\n// Format names for display\nconst formatName = (person)=>{\n    // Return dash if person doesn't exist, or if id is zero\n    return person && +person.id !== 0 ? \"\".concat(person.firstName, \" \").concat(person.surname) : \"\";\n};\n// Format members to notify for display\nconst formatMembersToNotify = (members)=>{\n    if (!members || !members.nodes || members.nodes.length === 0) {\n        return \"\";\n    }\n    return members.nodes.map((member)=>\"\".concat(member.firstName, \" \").concat(member.surname)).join(\", \");\n};\nconst IncidentRecordList = ()=>{\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [incidentRecords, setIncidentRecords] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    // Get vessel icon data for proper vessel icon display\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_14__.useVesselIconData)();\n    // GraphQL query to fetch incident records\n    const [getIncidentRecords, { loading: queryIncidentRecordsLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_graphql_queries__WEBPACK_IMPORTED_MODULE_3__.GET_INCIDENT_RECORDS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            if (response && response.readIncidentRecords) {\n                setIncidentRecords(response.readIncidentRecords.nodes);\n                setIsLoading(false);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error fetching incident records:\", error);\n            setIsLoading(false);\n        }\n    });\n    // Load incident records on initial render and when filter changes\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        loadIncidentRecords();\n    }, [\n        filter\n    ]);\n    // Function to load incident records with filters\n    const loadIncidentRecords = ()=>{\n        const variables = {\n            limit: 1000\n        };\n        // Add filters if they exist\n        if (Object.keys(filter).length > 0) {\n            const graphqlFilter = {};\n            // Add vessel filter\n            if (filter.vessel && filter.vessel.value) {\n                graphqlFilter.vesselID = {\n                    eq: filter.vessel.value\n                };\n            }\n            // Add date range filter\n            if (filter.dateRange) {\n                if (filter.dateRange.startDate) {\n                    graphqlFilter.startDate = {\n                        gte: dayjs__WEBPACK_IMPORTED_MODULE_4___default()(filter.dateRange.startDate).startOf(\"day\").toISOString()\n                    };\n                }\n                if (filter.dateRange.endDate) {\n                    graphqlFilter.endDate = {\n                        lte: dayjs__WEBPACK_IMPORTED_MODULE_4___default()(filter.dateRange.endDate).endOf(\"day\").toISOString()\n                    };\n                }\n            }\n            variables.filter = graphqlFilter;\n        }\n        getIncidentRecords({\n            variables\n        });\n    };\n    // Handle filter changes\n    const handleFilterChange = (param)=>{\n        let { type, data } = param;\n        setFilter((prevFilter)=>({\n                ...prevFilter,\n                [type]: data\n            }));\n    };\n    // Create column definitions for the DataTable\n    const columns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Title\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellClassName: \"w-full tablet-lg:w-auto\",\n            cell: (param)=>{\n                let { row } = param;\n                const incident = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full space-y-2 my-2.5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    href: \"/incident-records/edit/?id=\".concat(incident.id),\n                                    className: \"hover:text-curious-blue-400\",\n                                    children: incident.title || \"-\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.P, {\n                                    className: \" desktop:hidden\",\n                                    children: formatMembersToNotify(incident.membersToNotify)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm laptop:hidden\",\n                                    children: [\n                                        (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_5__.formatDate)(incident.startDate),\n                                        \" -\",\n                                        \" \",\n                                        (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_5__.formatDate)(incident.endDate)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2.5\",\n                                    children: [\n                                        incident.vessel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"tablet-md:hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_11__.VesselLocationDisplay, {\n                                                        vessel: getVesselWithIcon(incident.vessel.id, incident.vessel),\n                                                        vesselId: incident.vessel.id,\n                                                        displayText: false\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 41\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.Separator, {\n                                                    orientation: \"vertical\",\n                                                    className: \"h-4 tablet-md:hidden\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true),\n                                        incident.reportedBy && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_10__.Avatar, {\n                                            className: \"size-8 tablet-md:hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_10__.AvatarFallback, {\n                                                children: getCrewInitials(incident.reportedBy.firstName, incident.reportedBy.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"vessel\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Vessel\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-md\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const incident = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: incident.vessel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_11__.VesselLocationDisplay, {\n                        vessel: getVesselWithIcon(incident.vessel.id, incident.vessel),\n                        vesselId: incident.vessel.id,\n                        displayText: incident.vessel.title || \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"reportedBy\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Reported By\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 17\n                }, undefined);\n            },\n            breakpoint: \"tablet-md\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const incident = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: incident.reportedBy && incident.reportedBy.id > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2.5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.Tooltip, {\n                                mobileClickable: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.TooltipTrigger, {\n                                        mobileClickable: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_10__.Avatar, {\n                                            className: \"h-8 w-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_10__.AvatarFallback, {\n                                                className: \"text-xs\",\n                                                children: getCrewInitials(incident.reportedBy.firstName, incident.reportedBy.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.TooltipContent, {\n                                        className: \"lg:hidden\",\n                                        children: formatName(incident.reportedBy)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 33\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden lg:block\",\n                                children: formatName(incident.reportedBy)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 33\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = formatName(rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.reportedBy) || \"\";\n                const valueB = formatName(rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.reportedBy) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"startDate\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Start Date\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"laptop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const incident = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_5__.formatDate)(incident.startDate)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.startDate) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.startDate) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"endDate\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"End Date\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"laptop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const incident = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_5__.formatDate)(incident.endDate)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.endDate) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.endDate) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"membersToNotify\",\n            header: \"Members to Notify\",\n            cellAlignment: \"right\",\n            breakpoint: \"desktop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const incident = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: formatMembersToNotify(incident.membersToNotify)\n                }, void 0, false);\n            }\n        }\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full py-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_9__.ListHeader, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons_SealogsIncidentIcon__WEBPACK_IMPORTED_MODULE_12__.SealogsIncidentIcon, {\n                    className: \"w-8 h-8\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 23\n                }, void 0),\n                title: \"Incident Records\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.DropdownMenu, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.DropdownMenuTrigger, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_16__.SealogsCogIcon, {\n                                size: 36\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 29\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 25\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.DropdownMenuContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_18__.DropdownMenuItem, {\n                                onClick: ()=>{\n                                    router.push(\"/incident-records/create\");\n                                },\n                                children: \"New incident record\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 29\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 25\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                    lineNumber: 346,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                lineNumber: 342,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_incident_charts_dashboard__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                lineNumber: 363,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.DataTable, {\n                columns: columns,\n                data: incidentRecords,\n                noDataText: \"No incident records found\",\n                showToolbar: true,\n                isLoading: isLoading || queryIncidentRecordsLoading,\n                pageSize: 10,\n                onChange: handleFilterChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                lineNumber: 366,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n        lineNumber: 341,\n        columnNumber: 9\n    }, undefined);\n};\n_s(IncidentRecordList, \"1nsWXPP4mQoczwjopfjqAJ984NI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_14__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery\n    ];\n});\n_c = IncidentRecordList;\n/* harmony default export */ __webpack_exports__[\"default\"] = (IncidentRecordList);\nvar _c;\n$RefreshReg$(_c, \"IncidentRecordList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/incident-record/incident-record-list.tsx\n"));

/***/ })

});