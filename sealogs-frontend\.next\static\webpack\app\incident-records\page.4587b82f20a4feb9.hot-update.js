"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/incident-records/page",{

/***/ "(app-pages-browser)/./src/app/ui/incident-record/incident-record-list.tsx":
/*!*************************************************************!*\
  !*** ./src/app/ui/incident-record/incident-record-list.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _graphql_queries__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./graphql/queries */ \"(app-pages-browser)/./src/app/ui/incident-record/graphql/queries.ts\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/vessel-location-display */ \"(app-pages-browser)/./src/components/ui/vessel-location-display.tsx\");\n/* harmony import */ var _app_lib_icons_SealogsIncidentIcon__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/lib/icons/SealogsIncidentIcon */ \"(app-pages-browser)/./src/app/lib/icons/SealogsIncidentIcon.ts\");\n/* harmony import */ var _components_incident_charts_dashboard__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./components/incident-charts-dashboard */ \"(app-pages-browser)/./src/app/ui/incident-record/components/incident-charts-dashboard.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _app_lib_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/lib/icons */ \"(app-pages-browser)/./src/app/lib/icons/index.ts\");\n/* harmony import */ var _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @radix-ui/react-dropdown-menu */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-dropdown-me_8ec93851da28661231dcc925c4c21d7f/node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper functions\nconst getCrewInitials = (firstName, surname)=>{\n    var _firstName_charAt, _surname_charAt;\n    if (!firstName && !surname) return \"??\";\n    const first = (firstName === null || firstName === void 0 ? void 0 : (_firstName_charAt = firstName.charAt(0)) === null || _firstName_charAt === void 0 ? void 0 : _firstName_charAt.toUpperCase()) || \"\";\n    const last = (surname === null || surname === void 0 ? void 0 : (_surname_charAt = surname.charAt(0)) === null || _surname_charAt === void 0 ? void 0 : _surname_charAt.toUpperCase()) || \"\";\n    return \"\".concat(first).concat(last) || \"??\";\n};\n// Format names for display\nconst formatName = (person)=>{\n    // Return dash if person doesn't exist, or if id is zero\n    return person && +person.id !== 0 ? \"\".concat(person.firstName, \" \").concat(person.surname) : \"\";\n};\n// Format members to notify for display\nconst formatMembersToNotify = (members)=>{\n    if (!members || !members.nodes || members.nodes.length === 0) {\n        return \"\";\n    }\n    return members.nodes.map((member)=>\"\".concat(member.firstName, \" \").concat(member.surname)).join(\", \");\n};\nconst IncidentRecordList = ()=>{\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [incidentRecords, setIncidentRecords] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    // Get vessel icon data for proper vessel icon display\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_14__.useVesselIconData)();\n    // GraphQL query to fetch incident records\n    const [getIncidentRecords, { loading: queryIncidentRecordsLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_graphql_queries__WEBPACK_IMPORTED_MODULE_3__.GET_INCIDENT_RECORDS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            if (response && response.readIncidentRecords) {\n                setIncidentRecords(response.readIncidentRecords.nodes);\n                setIsLoading(false);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error fetching incident records:\", error);\n            setIsLoading(false);\n        }\n    });\n    // Load incident records on initial render and when filter changes\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        loadIncidentRecords();\n    }, [\n        filter\n    ]);\n    // Function to load incident records with filters\n    const loadIncidentRecords = ()=>{\n        const variables = {\n            limit: 1000\n        };\n        // Add filters if they exist\n        if (Object.keys(filter).length > 0) {\n            const graphqlFilter = {};\n            // Add vessel filter\n            if (filter.vessel && filter.vessel.value) {\n                graphqlFilter.vesselID = {\n                    eq: filter.vessel.value\n                };\n            }\n            // Add date range filter\n            if (filter.dateRange) {\n                if (filter.dateRange.startDate) {\n                    graphqlFilter.startDate = {\n                        gte: dayjs__WEBPACK_IMPORTED_MODULE_4___default()(filter.dateRange.startDate).startOf(\"day\").toISOString()\n                    };\n                }\n                if (filter.dateRange.endDate) {\n                    graphqlFilter.endDate = {\n                        lte: dayjs__WEBPACK_IMPORTED_MODULE_4___default()(filter.dateRange.endDate).endOf(\"day\").toISOString()\n                    };\n                }\n            }\n            variables.filter = graphqlFilter;\n        }\n        getIncidentRecords({\n            variables\n        });\n    };\n    // Handle filter changes\n    const handleFilterChange = (param)=>{\n        let { type, data } = param;\n        setFilter((prevFilter)=>({\n                ...prevFilter,\n                [type]: data\n            }));\n    };\n    // Create column definitions for the DataTable\n    const columns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Title\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellClassName: \"w-full tablet-lg:w-auto\",\n            cell: (param)=>{\n                let { row } = param;\n                const incident = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full space-y-2 my-2.5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    href: \"/incident-records/edit/?id=\".concat(incident.id),\n                                    className: \"hover:text-curious-blue-400\",\n                                    children: incident.title || \"-\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.P, {\n                                    className: \" desktop:hidden\",\n                                    children: formatMembersToNotify(incident.membersToNotify)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm laptop:hidden\",\n                                    children: [\n                                        (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_5__.formatDate)(incident.startDate),\n                                        \" -\",\n                                        \" \",\n                                        (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_5__.formatDate)(incident.endDate)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2.5\",\n                                    children: [\n                                        incident.vessel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"tablet-md:hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_11__.VesselLocationDisplay, {\n                                                        vessel: getVesselWithIcon(incident.vessel.id, incident.vessel),\n                                                        vesselId: incident.vessel.id,\n                                                        displayText: false\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 41\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.Separator, {\n                                                    orientation: \"vertical\",\n                                                    className: \"h-4 tablet-md:hidden\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true),\n                                        incident.reportedBy && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_10__.Avatar, {\n                                            className: \"size-8 tablet-md:hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_10__.AvatarFallback, {\n                                                children: getCrewInitials(incident.reportedBy.firstName, incident.reportedBy.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"membersToNotify\",\n            header: \"Members to Notify\",\n            cellAlignment: \"right\",\n            breakpoint: \"desktop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const incident = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: formatMembersToNotify(incident.membersToNotify)\n                }, void 0, false);\n            }\n        },\n        {\n            accessorKey: \"vessel\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Vessel\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-md\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const incident = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: incident.vessel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_11__.VesselLocationDisplay, {\n                        vessel: getVesselWithIcon(incident.vessel.id, incident.vessel),\n                        vesselId: incident.vessel.id,\n                        displayText: incident.vessel.title || \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"reportedBy\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Reported By\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 17\n                }, undefined);\n            },\n            breakpoint: \"tablet-md\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const incident = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: incident.reportedBy && incident.reportedBy.id > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2.5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.Tooltip, {\n                                mobileClickable: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.TooltipTrigger, {\n                                        mobileClickable: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_10__.Avatar, {\n                                            className: \"h-8 w-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_10__.AvatarFallback, {\n                                                className: \"text-xs\",\n                                                children: getCrewInitials(incident.reportedBy.firstName, incident.reportedBy.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.TooltipContent, {\n                                        className: \"lg:hidden\",\n                                        children: formatName(incident.reportedBy)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 33\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden lg:block\",\n                                children: formatName(incident.reportedBy)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 33\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = formatName(rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.reportedBy) || \"\";\n                const valueB = formatName(rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.reportedBy) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"startDate\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Start Date\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"laptop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const incident = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_5__.formatDate)(incident.startDate)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.startDate) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.startDate) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"endDate\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"End Date\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"laptop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const incident = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_5__.formatDate)(incident.endDate)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.endDate) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.endDate) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        }\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full py-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_9__.ListHeader, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons_SealogsIncidentIcon__WEBPACK_IMPORTED_MODULE_12__.SealogsIncidentIcon, {\n                    className: \"w-8 h-8\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                    lineNumber: 342,\n                    columnNumber: 23\n                }, void 0),\n                title: \"Incident Records\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.DropdownMenu, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.DropdownMenuTrigger, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_16__.SealogsCogIcon, {\n                                size: 36\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 29\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 25\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.DropdownMenuContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_18__.DropdownMenuItem, {\n                                onClick: ()=>{\n                                    router.push(\"/incident-records/create\");\n                                },\n                                children: \"New incident record\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 29\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 25\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                    lineNumber: 345,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                lineNumber: 341,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_incident_charts_dashboard__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                lineNumber: 362,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.DataTable, {\n                columns: columns,\n                data: incidentRecords,\n                noDataText: \"No incident records found\",\n                showToolbar: true,\n                isLoading: isLoading || queryIncidentRecordsLoading,\n                pageSize: 10,\n                onChange: handleFilterChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n                lineNumber: 365,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\incident-record\\\\incident-record-list.tsx\",\n        lineNumber: 340,\n        columnNumber: 9\n    }, undefined);\n};\n_s(IncidentRecordList, \"1nsWXPP4mQoczwjopfjqAJ984NI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_14__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery\n    ];\n});\n_c = IncidentRecordList;\n/* harmony default export */ __webpack_exports__[\"default\"] = (IncidentRecordList);\nvar _c;\n$RefreshReg$(_c, \"IncidentRecordList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdWkvaW5jaWRlbnQtcmVjb3JkL2luY2lkZW50LXJlY29yZC1saXN0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFMkM7QUFDQTtBQUNFO0FBQ1c7QUFDL0I7QUFDNEI7QUFDekI7QUFFeUM7QUFDSTtBQUNqQjtBQUNPO0FBQ2dCO0FBQ047QUFDRztBQUNaO0FBVXhDO0FBQ3dCO0FBQ2dCO0FBVWhFLG1CQUFtQjtBQUNuQixNQUFNNEIsa0JBQWtCLENBQUNDLFdBQW9CQztRQUUzQkQsbUJBQ0RDO0lBRmIsSUFBSSxDQUFDRCxhQUFhLENBQUNDLFNBQVMsT0FBTztJQUNuQyxNQUFNQyxRQUFRRixDQUFBQSxzQkFBQUEsaUNBQUFBLG9CQUFBQSxVQUFXRyxNQUFNLENBQUMsZ0JBQWxCSCx3Q0FBQUEsa0JBQXNCSSxXQUFXLE9BQU07SUFDckQsTUFBTUMsT0FBT0osQ0FBQUEsb0JBQUFBLCtCQUFBQSxrQkFBQUEsUUFBU0UsTUFBTSxDQUFDLGdCQUFoQkYsc0NBQUFBLGdCQUFvQkcsV0FBVyxPQUFNO0lBQ2xELE9BQU8sR0FBV0MsT0FBUkgsT0FBYSxPQUFMRyxTQUFVO0FBQ2hDO0FBRUEsMkJBQTJCO0FBQzNCLE1BQU1DLGFBQWEsQ0FBQ0M7SUFDaEIsd0RBQXdEO0lBQ3hELE9BQU9BLFVBQVUsQ0FBQ0EsT0FBT0MsRUFBRSxLQUFLLElBQzFCLEdBQXVCRCxPQUFwQkEsT0FBT1AsU0FBUyxFQUFDLEtBQWtCLE9BQWZPLE9BQU9OLE9BQU8sSUFDckM7QUFDVjtBQUVBLHVDQUF1QztBQUN2QyxNQUFNUSx3QkFBd0IsQ0FBQ0M7SUFDM0IsSUFBSSxDQUFDQSxXQUFXLENBQUNBLFFBQVFDLEtBQUssSUFBSUQsUUFBUUMsS0FBSyxDQUFDQyxNQUFNLEtBQUssR0FBRztRQUMxRCxPQUFPO0lBQ1g7SUFDQSxPQUFPRixRQUFRQyxLQUFLLENBQ2ZFLEdBQUcsQ0FBQyxDQUFDQyxTQUFnQixHQUF1QkEsT0FBcEJBLE9BQU9kLFNBQVMsRUFBQyxLQUFrQixPQUFmYyxPQUFPYixPQUFPLEdBQzFEYyxJQUFJLENBQUM7QUFDZDtBQUVBLE1BQU1DLHFCQUFxQjs7SUFDdkIsTUFBTUMsU0FBUzlDLDBEQUFTQTtJQUN4QixNQUFNLENBQUMrQyxXQUFXQyxhQUFhLEdBQUc5QywrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUMrQyxpQkFBaUJDLG1CQUFtQixHQUFHaEQsK0NBQVFBLENBQUMsRUFBRTtJQUV6RCxNQUFNLENBQUNpRCxRQUFRQyxVQUFVLEdBQUdsRCwrQ0FBUUEsQ0FBZSxDQUFDO0lBRXBELHNEQUFzRDtJQUN0RCxNQUFNLEVBQUVtRCxpQkFBaUIsRUFBRSxHQUFHcEMsK0VBQWlCQTtJQUUvQywwQ0FBMEM7SUFDMUMsTUFBTSxDQUFDcUMsb0JBQW9CLEVBQUVDLFNBQVNDLDJCQUEyQixFQUFFLENBQUMsR0FDaEVyRCw2REFBWUEsQ0FBQ0Msa0VBQW9CQSxFQUFFO1FBQy9CcUQsYUFBYTtRQUNiQyxhQUFhLENBQUNDO1lBQ1YsSUFBSUEsWUFBWUEsU0FBU0MsbUJBQW1CLEVBQUU7Z0JBQzFDVixtQkFBbUJTLFNBQVNDLG1CQUFtQixDQUFDcEIsS0FBSztnQkFDckRRLGFBQWE7WUFDakI7UUFDSjtRQUNBYSxTQUFTLENBQUNDO1lBQ05DLFFBQVFELEtBQUssQ0FBQyxvQ0FBb0NBO1lBQ2xEZCxhQUFhO1FBQ2pCO0lBQ0o7SUFFSixrRUFBa0U7SUFDbEUvQyxnREFBU0EsQ0FBQztRQUNOK0Q7SUFDSixHQUFHO1FBQUNiO0tBQU87SUFFWCxpREFBaUQ7SUFDakQsTUFBTWEsc0JBQXNCO1FBQ3hCLE1BQU1DLFlBQWlCO1lBQ25CQyxPQUFPO1FBQ1g7UUFFQSw0QkFBNEI7UUFDNUIsSUFBSUMsT0FBT0MsSUFBSSxDQUFDakIsUUFBUVYsTUFBTSxHQUFHLEdBQUc7WUFDaEMsTUFBTTRCLGdCQUFxQixDQUFDO1lBRTVCLG9CQUFvQjtZQUNwQixJQUFJbEIsT0FBT21CLE1BQU0sSUFBSW5CLE9BQU9tQixNQUFNLENBQUNDLEtBQUssRUFBRTtnQkFDdENGLGNBQWNHLFFBQVEsR0FBRztvQkFBRUMsSUFBSXRCLE9BQU9tQixNQUFNLENBQUNDLEtBQUs7Z0JBQUM7WUFDdkQ7WUFFQSx3QkFBd0I7WUFDeEIsSUFBSXBCLE9BQU91QixTQUFTLEVBQUU7Z0JBQ2xCLElBQUl2QixPQUFPdUIsU0FBUyxDQUFDQyxTQUFTLEVBQUU7b0JBQzVCTixjQUFjTSxTQUFTLEdBQUc7d0JBQ3RCQyxLQUFLdkUsNENBQUtBLENBQUM4QyxPQUFPdUIsU0FBUyxDQUFDQyxTQUFTLEVBQ2hDRSxPQUFPLENBQUMsT0FDUkMsV0FBVztvQkFDcEI7Z0JBQ0o7Z0JBQ0EsSUFBSTNCLE9BQU91QixTQUFTLENBQUNLLE9BQU8sRUFBRTtvQkFDMUJWLGNBQWNVLE9BQU8sR0FBRzt3QkFDcEJDLEtBQUszRSw0Q0FBS0EsQ0FBQzhDLE9BQU91QixTQUFTLENBQUNLLE9BQU8sRUFDOUJFLEtBQUssQ0FBQyxPQUNOSCxXQUFXO29CQUNwQjtnQkFDSjtZQUNKO1lBRUFiLFVBQVVkLE1BQU0sR0FBR2tCO1FBQ3ZCO1FBRUFmLG1CQUFtQjtZQUFFVztRQUFVO0lBQ25DO0lBRUEsd0JBQXdCO0lBQ3hCLE1BQU1pQixxQkFBcUI7WUFBQyxFQUFFQyxJQUFJLEVBQUVDLElBQUksRUFBTztRQUMzQ2hDLFVBQVUsQ0FBQ2lDLGFBQWdCO2dCQUN2QixHQUFHQSxVQUFVO2dCQUNiLENBQUNGLEtBQUssRUFBRUM7WUFDWjtJQUNKO0lBRUEsOENBQThDO0lBQzlDLE1BQU1FLFVBQVU3RSx3RUFBYUEsQ0FBQztRQUMxQjtZQUNJOEUsYUFBYTtZQUNiQyxRQUFRO29CQUFDLEVBQUVDLE1BQU0sRUFBbUI7cUNBQ2hDLDhEQUFDL0UsbUZBQW1CQTtvQkFBQytFLFFBQVFBO29CQUFRQyxPQUFNOzs7Ozs7O1lBRS9DQyxlQUFlO1lBQ2ZDLE1BQU07b0JBQUMsRUFBRUMsR0FBRyxFQUFnQjtnQkFDeEIsTUFBTUMsV0FBV0QsSUFBSUUsUUFBUTtnQkFDN0IscUJBQ0ksOERBQUNDO29CQUFJQyxXQUFVOztzQ0FDWCw4REFBQ0Q7OzhDQUNHLDhEQUFDekYsaURBQUlBO29DQUNEMkYsTUFBTSw4QkFBMEMsT0FBWkosU0FBU3pELEVBQUU7b0NBQy9DNEQsV0FBVTs4Q0FDVEgsU0FBU0osS0FBSyxJQUFJOzs7Ozs7OENBRXZCLDhEQUFDckUsOENBQUNBO29DQUFDNEUsV0FBVTs4Q0FDUjNELHNCQUNHd0QsU0FBU0ssZUFBZTs7Ozs7Ozs7Ozs7O3NDQUlwQyw4REFBQ0g7NEJBQUlDLFdBQVU7OzhDQUNYLDhEQUFDRztvQ0FBRUgsV0FBVTs7d0NBQ1IzRixtRUFBVUEsQ0FBQ3dGLFNBQVNuQixTQUFTO3dDQUFFO3dDQUFHO3dDQUNsQ3JFLG1FQUFVQSxDQUFDd0YsU0FBU2YsT0FBTzs7Ozs7Ozs4Q0FHaEMsOERBQUNpQjtvQ0FBSUMsV0FBVTs7d0NBQ1ZILFNBQVN4QixNQUFNLGtCQUNaOzs4REFDSSw4REFBQzBCO29EQUFJQyxXQUFVOzhEQUNYLDRFQUFDbkYsMEZBQXFCQTt3REFDbEJ3RCxRQUFRakIsa0JBQ0p5QyxTQUFTeEIsTUFBTSxDQUFDakMsRUFBRSxFQUNsQnlELFNBQVN4QixNQUFNO3dEQUVuQitCLFVBQVVQLFNBQVN4QixNQUFNLENBQUNqQyxFQUFFO3dEQUM1QmlFLGFBQWE7Ozs7Ozs7Ozs7OzhEQUdyQiw4REFBQ2hGLHNEQUFTQTtvREFDTmlGLGFBQVk7b0RBQ1pOLFdBQVU7Ozs7Ozs7O3dDQUtyQkgsU0FBU1UsVUFBVSxrQkFDaEIsOERBQUM1RiwwREFBTUE7NENBQUNxRixXQUFVO3NEQUNkLDRFQUFDcEYsa0VBQWNBOzBEQUNWZSxnQkFDR2tFLFNBQVNVLFVBQVUsQ0FBQzNFLFNBQVMsRUFDN0JpRSxTQUFTVSxVQUFVLENBQUMxRSxPQUFPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVMvRDtZQUNBMkUsV0FBVyxDQUFDQyxNQUFXQztvQkFDSkQsZ0JBQ0FDO2dCQURmLE1BQU1DLFNBQVNGLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1YLFFBQVEsY0FBZFcscUNBQUFBLGVBQWdCaEIsS0FBSyxLQUFJO2dCQUN4QyxNQUFNbUIsU0FBU0YsQ0FBQUEsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTVosUUFBUSxjQUFkWSxxQ0FBQUEsZUFBZ0JqQixLQUFLLEtBQUk7Z0JBQ3hDLE9BQU9rQixPQUFPRSxhQUFhLENBQUNEO1lBQ2hDO1FBQ0o7UUFDQTtZQUNJdEIsYUFBYTtZQUNiQyxRQUFRO1lBQ1J1QixlQUFlO1lBQ2ZDLFlBQVk7WUFDWnJCLGVBQWU7WUFDZkMsTUFBTTtvQkFBQyxFQUFFQyxHQUFHLEVBQWdCO2dCQUN4QixNQUFNQyxXQUFXRCxJQUFJRSxRQUFRO2dCQUM3QixxQkFBTzs4QkFBR3pELHNCQUFzQndELFNBQVNLLGVBQWU7O1lBQzVEO1FBQ0o7UUFDQTtZQUNJWixhQUFhO1lBQ2JDLFFBQVE7b0JBQUMsRUFBRUMsTUFBTSxFQUFtQjtxQ0FDaEMsOERBQUMvRSxtRkFBbUJBO29CQUFDK0UsUUFBUUE7b0JBQVFDLE9BQU07Ozs7Ozs7WUFFL0NxQixlQUFlO1lBQ2ZDLFlBQVk7WUFDWnJCLGVBQWU7WUFDZkMsTUFBTTtvQkFBQyxFQUFFQyxHQUFHLEVBQWdCO2dCQUN4QixNQUFNQyxXQUFXRCxJQUFJRSxRQUFRO2dCQUU3QixxQkFDSTs4QkFDS0QsU0FBU3hCLE1BQU0sa0JBQ1osOERBQUN4RCwwRkFBcUJBO3dCQUNsQndELFFBQVFqQixrQkFDSnlDLFNBQVN4QixNQUFNLENBQUNqQyxFQUFFLEVBQ2xCeUQsU0FBU3hCLE1BQU07d0JBRW5CK0IsVUFBVVAsU0FBU3hCLE1BQU0sQ0FBQ2pDLEVBQUU7d0JBQzVCaUUsYUFBYVIsU0FBU3hCLE1BQU0sQ0FBQ29CLEtBQUssSUFBSTs7Ozs7OztZQUsxRDtZQUNBZSxXQUFXLENBQUNDLE1BQVdDO29CQUNKRCx1QkFBQUEsZ0JBQ0FDLHVCQUFBQTtnQkFEZixNQUFNQyxTQUFTRixDQUFBQSxpQkFBQUEsNEJBQUFBLGlCQUFBQSxLQUFNWCxRQUFRLGNBQWRXLHNDQUFBQSx3QkFBQUEsZUFBZ0JwQyxNQUFNLGNBQXRCb0MsNENBQUFBLHNCQUF3QmhCLEtBQUssS0FBSTtnQkFDaEQsTUFBTW1CLFNBQVNGLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1aLFFBQVEsY0FBZFksc0NBQUFBLHdCQUFBQSxlQUFnQnJDLE1BQU0sY0FBdEJxQyw0Q0FBQUEsc0JBQXdCakIsS0FBSyxLQUFJO2dCQUNoRCxPQUFPa0IsT0FBT0UsYUFBYSxDQUFDRDtZQUNoQztRQUNKO1FBQ0E7WUFDSXRCLGFBQWE7WUFDYkMsUUFBUTtvQkFBQyxFQUFFQyxNQUFNLEVBQW1CO3FDQUNoQyw4REFBQy9FLG1GQUFtQkE7b0JBQUMrRSxRQUFRQTtvQkFBUUMsT0FBTTs7Ozs7OztZQUUvQ3NCLFlBQVk7WUFDWnJCLGVBQWU7WUFDZkMsTUFBTTtvQkFBQyxFQUFFQyxHQUFHLEVBQWdCO2dCQUN4QixNQUFNQyxXQUFXRCxJQUFJRSxRQUFRO2dCQUM3QixxQkFDSTs4QkFDS0QsU0FBU1UsVUFBVSxJQUFJVixTQUFTVSxVQUFVLENBQUNuRSxFQUFFLEdBQUcsbUJBQzdDLDhEQUFDMkQ7d0JBQUlDLFdBQVU7OzBDQUNYLDhEQUFDMUUsb0RBQU9BO2dDQUFDMEYsZUFBZTs7a0RBQ3BCLDhEQUFDeEYsMkRBQWNBO3dDQUFDd0YsZUFBZTtrREFDM0IsNEVBQUNyRywwREFBTUE7NENBQUNxRixXQUFVO3NEQUNkLDRFQUFDcEYsa0VBQWNBO2dEQUFDb0YsV0FBVTswREFDckJyRSxnQkFDR2tFLFNBQVNVLFVBQVUsQ0FDZDNFLFNBQVMsRUFDZGlFLFNBQVNVLFVBQVUsQ0FBQzFFLE9BQU87Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSzNDLDhEQUFDTiwyREFBY0E7d0NBQUN5RSxXQUFVO2tEQUNyQjlELFdBQVcyRCxTQUFTVSxVQUFVOzs7Ozs7Ozs7Ozs7MENBR3ZDLDhEQUFDVTtnQ0FBS2pCLFdBQVU7MENBQ1g5RCxXQUFXMkQsU0FBU1UsVUFBVTs7Ozs7Ozs7Ozs7OztZQU12RDtZQUNBQyxXQUFXLENBQUNDLE1BQVdDO29CQUNPRCxnQkFDQUM7Z0JBRDFCLE1BQU1DLFNBQVN6RSxXQUFXdUUsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTVgsUUFBUSxjQUFkVyxxQ0FBQUEsZUFBZ0JGLFVBQVUsS0FBSztnQkFDekQsTUFBTUssU0FBUzFFLFdBQVd3RSxpQkFBQUEsNEJBQUFBLGlCQUFBQSxLQUFNWixRQUFRLGNBQWRZLHFDQUFBQSxlQUFnQkgsVUFBVSxLQUFLO2dCQUN6RCxPQUFPSSxPQUFPRSxhQUFhLENBQUNEO1lBQ2hDO1FBQ0o7UUFDQTtZQUNJdEIsYUFBYTtZQUNiQyxRQUFRO29CQUFDLEVBQUVDLE1BQU0sRUFBbUI7cUNBQ2hDLDhEQUFDL0UsbUZBQW1CQTtvQkFBQytFLFFBQVFBO29CQUFRQyxPQUFNOzs7Ozs7O1lBRS9DcUIsZUFBZTtZQUNmQyxZQUFZO1lBQ1pyQixlQUFlO1lBQ2ZDLE1BQU07b0JBQUMsRUFBRUMsR0FBRyxFQUFnQjtnQkFDeEIsTUFBTUMsV0FBV0QsSUFBSUUsUUFBUTtnQkFDN0IscUJBQU87OEJBQUd6RixtRUFBVUEsQ0FBQ3dGLFNBQVNuQixTQUFTOztZQUMzQztZQUNBOEIsV0FBVyxDQUFDQyxNQUFXQztvQkFDSkQsZ0JBQ0FDO2dCQURmLE1BQU1DLFNBQVNGLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1YLFFBQVEsY0FBZFcscUNBQUFBLGVBQWdCL0IsU0FBUyxLQUFJO2dCQUM1QyxNQUFNa0MsU0FBU0YsQ0FBQUEsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTVosUUFBUSxjQUFkWSxxQ0FBQUEsZUFBZ0JoQyxTQUFTLEtBQUk7Z0JBQzVDLE9BQU9pQyxPQUFPRSxhQUFhLENBQUNEO1lBQ2hDO1FBQ0o7UUFDQTtZQUNJdEIsYUFBYTtZQUNiQyxRQUFRO29CQUFDLEVBQUVDLE1BQU0sRUFBbUI7cUNBQ2hDLDhEQUFDL0UsbUZBQW1CQTtvQkFBQytFLFFBQVFBO29CQUFRQyxPQUFNOzs7Ozs7O1lBRS9DcUIsZUFBZTtZQUNmQyxZQUFZO1lBQ1pyQixlQUFlO1lBQ2ZDLE1BQU07b0JBQUMsRUFBRUMsR0FBRyxFQUFnQjtnQkFDeEIsTUFBTUMsV0FBV0QsSUFBSUUsUUFBUTtnQkFDN0IscUJBQU87OEJBQUd6RixtRUFBVUEsQ0FBQ3dGLFNBQVNmLE9BQU87O1lBQ3pDO1lBQ0EwQixXQUFXLENBQUNDLE1BQVdDO29CQUNKRCxnQkFDQUM7Z0JBRGYsTUFBTUMsU0FBU0YsQ0FBQUEsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTVgsUUFBUSxjQUFkVyxxQ0FBQUEsZUFBZ0IzQixPQUFPLEtBQUk7Z0JBQzFDLE1BQU04QixTQUFTRixDQUFBQSxpQkFBQUEsNEJBQUFBLGlCQUFBQSxLQUFNWixRQUFRLGNBQWRZLHFDQUFBQSxlQUFnQjVCLE9BQU8sS0FBSTtnQkFDMUMsT0FBTzZCLE9BQU9FLGFBQWEsQ0FBQ0Q7WUFDaEM7UUFDSjtLQUNIO0lBRUQscUJBQ0ksOERBQUNiO1FBQUlDLFdBQVU7OzBCQUNYLDhEQUFDdEYsa0VBQVVBO2dCQUNQd0csb0JBQU0sOERBQUNwRyxvRkFBbUJBO29CQUFDa0YsV0FBVTs7Ozs7O2dCQUNyQ1AsT0FBTTtnQkFDTjBCLHVCQUNJLDhEQUFDbEcseURBQVlBOztzQ0FDVCw4REFBQ0UsZ0VBQW1CQTs0QkFBQ2lHLE9BQU87c0NBQ3hCLDRFQUFDM0YsMkRBQWNBO2dDQUFDNEYsTUFBTTs7Ozs7Ozs7Ozs7c0NBRTFCLDhEQUFDbkcsZ0VBQW1CQTtzQ0FDaEIsNEVBQUNRLDRFQUFnQkE7Z0NBQ2I0RixTQUFTO29DQUNMekUsT0FBTzBFLElBQUksQ0FBQztnQ0FDaEI7MENBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBU3ZCLDhEQUFDeEcsOEVBQXVCQTs7Ozs7MEJBR3hCLDhEQUFDUixnRUFBU0E7Z0JBQ044RSxTQUFTQTtnQkFDVEYsTUFBTW5DO2dCQUNOd0UsWUFBVztnQkFDWEMsYUFBYTtnQkFDYjNFLFdBQVdBLGFBQWFTO2dCQUN4Qm1FLFVBQVU7Z0JBQ1ZDLFVBQVUxQzs7Ozs7Ozs7Ozs7O0FBSTFCO0dBdFRNckM7O1FBQ2E3QyxzREFBU0E7UUFPTWlCLDJFQUFpQkE7UUFJM0NkLHlEQUFZQTs7O0tBWmQwQztBQXVUTiwrREFBZUEsa0JBQWtCQSxFQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvdWkvaW5jaWRlbnQtcmVjb3JkL2luY2lkZW50LXJlY29yZC1saXN0LnRzeD9kNDg0Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xyXG5cclxuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJ1xyXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXHJcbmltcG9ydCB7IHVzZUxhenlRdWVyeSB9IGZyb20gJ0BhcG9sbG8vY2xpZW50J1xyXG5pbXBvcnQgeyBHRVRfSU5DSURFTlRfUkVDT1JEUyB9IGZyb20gJy4vZ3JhcGhxbC9xdWVyaWVzJ1xyXG5pbXBvcnQgZGF5anMgZnJvbSAnZGF5anMnXHJcbmltcG9ydCB7IGZvcm1hdERhdGUgfSBmcm9tICdAL2FwcC9oZWxwZXJzL2RhdGVIZWxwZXInXHJcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluaydcclxuaW1wb3J0IFNlYUxvZ3NCdXR0b24gZnJvbSAnQC9jb21wb25lbnRzL3VpL3NlYS1sb2dzLWJ1dHRvbidcclxuaW1wb3J0IHsgRGF0YVRhYmxlLCBjcmVhdGVDb2x1bW5zIH0gZnJvbSAnQC9jb21wb25lbnRzL2ZpbHRlcmVkVGFibGUnXHJcbmltcG9ydCB7IERhdGFUYWJsZVNvcnRIZWFkZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvZGF0YS10YWJsZS1zb3J0LWhlYWRlcidcclxuaW1wb3J0IHsgTGlzdEhlYWRlciB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9saXN0LWhlYWRlcidcclxuaW1wb3J0IHsgQXZhdGFyLCBBdmF0YXJGYWxsYmFjayB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9hdmF0YXInXHJcbmltcG9ydCB7IFZlc3NlbExvY2F0aW9uRGlzcGxheSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS92ZXNzZWwtbG9jYXRpb24tZGlzcGxheSdcclxuaW1wb3J0IHsgU2VhbG9nc0luY2lkZW50SWNvbiB9IGZyb20gJ0AvYXBwL2xpYi9pY29ucy9TZWFsb2dzSW5jaWRlbnRJY29uJ1xyXG5pbXBvcnQgSW5jaWRlbnRDaGFydHNEYXNoYm9hcmQgZnJvbSAnLi9jb21wb25lbnRzL2luY2lkZW50LWNoYXJ0cy1kYXNoYm9hcmQnXHJcbmltcG9ydCB7IHVzZVZlc3NlbEljb25EYXRhIH0gZnJvbSAnQC9hcHAvbGliL3Zlc3NlbC1pY29uLWhlbHBlcidcclxuaW1wb3J0IHtcclxuICAgIERyb3Bkb3duTWVudSxcclxuICAgIERyb3Bkb3duTWVudUNvbnRlbnQsXHJcbiAgICBEcm9wZG93bk1lbnVUcmlnZ2VyLFxyXG4gICAgUCxcclxuICAgIFNlcGFyYXRvcixcclxuICAgIFRvb2x0aXAsXHJcbiAgICBUb29sdGlwQ29udGVudCxcclxuICAgIFRvb2x0aXBUcmlnZ2VyLFxyXG59IGZyb20gJ0AvY29tcG9uZW50cy91aSdcclxuaW1wb3J0IHsgU2VhbG9nc0NvZ0ljb24gfSBmcm9tICdAL2FwcC9saWIvaWNvbnMnXHJcbmltcG9ydCB7IERyb3Bkb3duTWVudUl0ZW0gfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtZHJvcGRvd24tbWVudSdcclxuXHJcbmludGVyZmFjZSBTZWFyY2hGaWx0ZXIge1xyXG4gICAgdmVzc2VsPzogYW55XHJcbiAgICBkYXRlUmFuZ2U/OiB7XHJcbiAgICAgICAgc3RhcnREYXRlOiBzdHJpbmcgfCBudWxsXHJcbiAgICAgICAgZW5kRGF0ZTogc3RyaW5nIHwgbnVsbFxyXG4gICAgfVxyXG59XHJcblxyXG4vLyBIZWxwZXIgZnVuY3Rpb25zXHJcbmNvbnN0IGdldENyZXdJbml0aWFscyA9IChmaXJzdE5hbWU/OiBzdHJpbmcsIHN1cm5hbWU/OiBzdHJpbmcpOiBzdHJpbmcgPT4ge1xyXG4gICAgaWYgKCFmaXJzdE5hbWUgJiYgIXN1cm5hbWUpIHJldHVybiAnPz8nXHJcbiAgICBjb25zdCBmaXJzdCA9IGZpcnN0TmFtZT8uY2hhckF0KDApPy50b1VwcGVyQ2FzZSgpIHx8ICcnXHJcbiAgICBjb25zdCBsYXN0ID0gc3VybmFtZT8uY2hhckF0KDApPy50b1VwcGVyQ2FzZSgpIHx8ICcnXHJcbiAgICByZXR1cm4gYCR7Zmlyc3R9JHtsYXN0fWAgfHwgJz8/J1xyXG59XHJcblxyXG4vLyBGb3JtYXQgbmFtZXMgZm9yIGRpc3BsYXlcclxuY29uc3QgZm9ybWF0TmFtZSA9IChwZXJzb246IGFueSkgPT4ge1xyXG4gICAgLy8gUmV0dXJuIGRhc2ggaWYgcGVyc29uIGRvZXNuJ3QgZXhpc3QsIG9yIGlmIGlkIGlzIHplcm9cclxuICAgIHJldHVybiBwZXJzb24gJiYgK3BlcnNvbi5pZCAhPT0gMFxyXG4gICAgICAgID8gYCR7cGVyc29uLmZpcnN0TmFtZX0gJHtwZXJzb24uc3VybmFtZX1gXHJcbiAgICAgICAgOiAnJ1xyXG59XHJcblxyXG4vLyBGb3JtYXQgbWVtYmVycyB0byBub3RpZnkgZm9yIGRpc3BsYXlcclxuY29uc3QgZm9ybWF0TWVtYmVyc1RvTm90aWZ5ID0gKG1lbWJlcnM6IGFueSkgPT4ge1xyXG4gICAgaWYgKCFtZW1iZXJzIHx8ICFtZW1iZXJzLm5vZGVzIHx8IG1lbWJlcnMubm9kZXMubGVuZ3RoID09PSAwKSB7XHJcbiAgICAgICAgcmV0dXJuICcnXHJcbiAgICB9XHJcbiAgICByZXR1cm4gbWVtYmVycy5ub2Rlc1xyXG4gICAgICAgIC5tYXAoKG1lbWJlcjogYW55KSA9PiBgJHttZW1iZXIuZmlyc3ROYW1lfSAke21lbWJlci5zdXJuYW1lfWApXHJcbiAgICAgICAgLmpvaW4oJywgJylcclxufVxyXG5cclxuY29uc3QgSW5jaWRlbnRSZWNvcmRMaXN0ID0gKCkgPT4ge1xyXG4gICAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKClcclxuICAgIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKVxyXG4gICAgY29uc3QgW2luY2lkZW50UmVjb3Jkcywgc2V0SW5jaWRlbnRSZWNvcmRzXSA9IHVzZVN0YXRlKFtdKVxyXG5cclxuICAgIGNvbnN0IFtmaWx0ZXIsIHNldEZpbHRlcl0gPSB1c2VTdGF0ZTxTZWFyY2hGaWx0ZXI+KHt9KVxyXG5cclxuICAgIC8vIEdldCB2ZXNzZWwgaWNvbiBkYXRhIGZvciBwcm9wZXIgdmVzc2VsIGljb24gZGlzcGxheVxyXG4gICAgY29uc3QgeyBnZXRWZXNzZWxXaXRoSWNvbiB9ID0gdXNlVmVzc2VsSWNvbkRhdGEoKVxyXG5cclxuICAgIC8vIEdyYXBoUUwgcXVlcnkgdG8gZmV0Y2ggaW5jaWRlbnQgcmVjb3Jkc1xyXG4gICAgY29uc3QgW2dldEluY2lkZW50UmVjb3JkcywgeyBsb2FkaW5nOiBxdWVyeUluY2lkZW50UmVjb3Jkc0xvYWRpbmcgfV0gPVxyXG4gICAgICAgIHVzZUxhenlRdWVyeShHRVRfSU5DSURFTlRfUkVDT1JEUywge1xyXG4gICAgICAgICAgICBmZXRjaFBvbGljeTogJ2NhY2hlLWFuZC1uZXR3b3JrJyxcclxuICAgICAgICAgICAgb25Db21wbGV0ZWQ6IChyZXNwb25zZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlICYmIHJlc3BvbnNlLnJlYWRJbmNpZGVudFJlY29yZHMpIHtcclxuICAgICAgICAgICAgICAgICAgICBzZXRJbmNpZGVudFJlY29yZHMocmVzcG9uc2UucmVhZEluY2lkZW50UmVjb3Jkcy5ub2RlcylcclxuICAgICAgICAgICAgICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIG9uRXJyb3I6IChlcnJvcikgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgaW5jaWRlbnQgcmVjb3JkczonLCBlcnJvcilcclxuICAgICAgICAgICAgICAgIHNldElzTG9hZGluZyhmYWxzZSlcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9KVxyXG5cclxuICAgIC8vIExvYWQgaW5jaWRlbnQgcmVjb3JkcyBvbiBpbml0aWFsIHJlbmRlciBhbmQgd2hlbiBmaWx0ZXIgY2hhbmdlc1xyXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgICAgICBsb2FkSW5jaWRlbnRSZWNvcmRzKClcclxuICAgIH0sIFtmaWx0ZXJdKVxyXG5cclxuICAgIC8vIEZ1bmN0aW9uIHRvIGxvYWQgaW5jaWRlbnQgcmVjb3JkcyB3aXRoIGZpbHRlcnNcclxuICAgIGNvbnN0IGxvYWRJbmNpZGVudFJlY29yZHMgPSAoKSA9PiB7XHJcbiAgICAgICAgY29uc3QgdmFyaWFibGVzOiBhbnkgPSB7XHJcbiAgICAgICAgICAgIGxpbWl0OiAxMDAwLCAvLyBGZXRjaCBtb3JlIHJlY29yZHMgZm9yIGNsaWVudC1zaWRlIHBhZ2luYXRpb25cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIEFkZCBmaWx0ZXJzIGlmIHRoZXkgZXhpc3RcclxuICAgICAgICBpZiAoT2JqZWN0LmtleXMoZmlsdGVyKS5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IGdyYXBocWxGaWx0ZXI6IGFueSA9IHt9XHJcblxyXG4gICAgICAgICAgICAvLyBBZGQgdmVzc2VsIGZpbHRlclxyXG4gICAgICAgICAgICBpZiAoZmlsdGVyLnZlc3NlbCAmJiBmaWx0ZXIudmVzc2VsLnZhbHVlKSB7XHJcbiAgICAgICAgICAgICAgICBncmFwaHFsRmlsdGVyLnZlc3NlbElEID0geyBlcTogZmlsdGVyLnZlc3NlbC52YWx1ZSB9XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIC8vIEFkZCBkYXRlIHJhbmdlIGZpbHRlclxyXG4gICAgICAgICAgICBpZiAoZmlsdGVyLmRhdGVSYW5nZSkge1xyXG4gICAgICAgICAgICAgICAgaWYgKGZpbHRlci5kYXRlUmFuZ2Uuc3RhcnREYXRlKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgZ3JhcGhxbEZpbHRlci5zdGFydERhdGUgPSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGd0ZTogZGF5anMoZmlsdGVyLmRhdGVSYW5nZS5zdGFydERhdGUpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAuc3RhcnRPZignZGF5JylcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC50b0lTT1N0cmluZygpLFxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIGlmIChmaWx0ZXIuZGF0ZVJhbmdlLmVuZERhdGUpIHtcclxuICAgICAgICAgICAgICAgICAgICBncmFwaHFsRmlsdGVyLmVuZERhdGUgPSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGx0ZTogZGF5anMoZmlsdGVyLmRhdGVSYW5nZS5lbmREYXRlKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLmVuZE9mKCdkYXknKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICB2YXJpYWJsZXMuZmlsdGVyID0gZ3JhcGhxbEZpbHRlclxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgZ2V0SW5jaWRlbnRSZWNvcmRzKHsgdmFyaWFibGVzIH0pXHJcbiAgICB9XHJcblxyXG4gICAgLy8gSGFuZGxlIGZpbHRlciBjaGFuZ2VzXHJcbiAgICBjb25zdCBoYW5kbGVGaWx0ZXJDaGFuZ2UgPSAoeyB0eXBlLCBkYXRhIH06IGFueSkgPT4ge1xyXG4gICAgICAgIHNldEZpbHRlcigocHJldkZpbHRlcikgPT4gKHtcclxuICAgICAgICAgICAgLi4ucHJldkZpbHRlcixcclxuICAgICAgICAgICAgW3R5cGVdOiBkYXRhLFxyXG4gICAgICAgIH0pKVxyXG4gICAgfVxyXG5cclxuICAgIC8vIENyZWF0ZSBjb2x1bW4gZGVmaW5pdGlvbnMgZm9yIHRoZSBEYXRhVGFibGVcclxuICAgIGNvbnN0IGNvbHVtbnMgPSBjcmVhdGVDb2x1bW5zKFtcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiAndGl0bGUnLFxyXG4gICAgICAgICAgICBoZWFkZXI6ICh7IGNvbHVtbiB9OiB7IGNvbHVtbjogYW55IH0pID0+IChcclxuICAgICAgICAgICAgICAgIDxEYXRhVGFibGVTb3J0SGVhZGVyIGNvbHVtbj17Y29sdW1ufSB0aXRsZT1cIlRpdGxlXCIgLz5cclxuICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgY2VsbENsYXNzTmFtZTogJ3ctZnVsbCB0YWJsZXQtbGc6dy1hdXRvJyxcclxuICAgICAgICAgICAgY2VsbDogKHsgcm93IH06IHsgcm93OiBhbnkgfSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgaW5jaWRlbnQgPSByb3cub3JpZ2luYWxcclxuICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgc3BhY2UteS0yIG15LTIuNVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPXtgL2luY2lkZW50LXJlY29yZHMvZWRpdC8/aWQ9JHtpbmNpZGVudC5pZH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImhvdmVyOnRleHQtY3VyaW91cy1ibHVlLTQwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtpbmNpZGVudC50aXRsZSB8fCAnLSd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8UCBjbGFzc05hbWU9XCIgZGVza3RvcDpoaWRkZW5cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0TWVtYmVyc1RvTm90aWZ5KFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbmNpZGVudC5tZW1iZXJzVG9Ob3RpZnksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvUD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIGdhcC00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGxhcHRvcDpoaWRkZW5cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0RGF0ZShpbmNpZGVudC5zdGFydERhdGUpfSAteycgJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0RGF0ZShpbmNpZGVudC5lbmREYXRlKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBNb2JpbGU6IFNob3cgdmVzc2VsIGFuZCByZXBvcnRlZCBieSAqL31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIuNVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtpbmNpZGVudC52ZXNzZWwgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0YWJsZXQtbWQ6aGlkZGVuXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFZlc3NlbExvY2F0aW9uRGlzcGxheVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWw9e2dldFZlc3NlbFdpdGhJY29uKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5jaWRlbnQudmVzc2VsLmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5jaWRlbnQudmVzc2VsLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWxJZD17aW5jaWRlbnQudmVzc2VsLmlkfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5VGV4dD17ZmFsc2V9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlcGFyYXRvclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9yaWVudGF0aW9uPVwidmVydGljYWxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtNCB0YWJsZXQtbWQ6aGlkZGVuXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtpbmNpZGVudC5yZXBvcnRlZEJ5ICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEF2YXRhciBjbGFzc05hbWU9XCJzaXplLTggdGFibGV0LW1kOmhpZGRlblwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEF2YXRhckZhbGxiYWNrPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtnZXRDcmV3SW5pdGlhbHMoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluY2lkZW50LnJlcG9ydGVkQnkuZmlyc3ROYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbmNpZGVudC5yZXBvcnRlZEJ5LnN1cm5hbWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQXZhdGFyRmFsbGJhY2s+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQXZhdGFyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIHNvcnRpbmdGbjogKHJvd0E6IGFueSwgcm93QjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZUEgPSByb3dBPy5vcmlnaW5hbD8udGl0bGUgfHwgJydcclxuICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlQiA9IHJvd0I/Lm9yaWdpbmFsPy50aXRsZSB8fCAnJ1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlQS5sb2NhbGVDb21wYXJlKHZhbHVlQilcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6ICdtZW1iZXJzVG9Ob3RpZnknLFxyXG4gICAgICAgICAgICBoZWFkZXI6ICdNZW1iZXJzIHRvIE5vdGlmeScsXHJcbiAgICAgICAgICAgIGNlbGxBbGlnbm1lbnQ6ICdyaWdodCcgYXMgY29uc3QsXHJcbiAgICAgICAgICAgIGJyZWFrcG9pbnQ6ICdkZXNrdG9wJyBhcyBjb25zdCxcclxuICAgICAgICAgICAgY2VsbENsYXNzTmFtZTogJ3B4LTIuNScsXHJcbiAgICAgICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogYW55IH0pID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGluY2lkZW50ID0gcm93Lm9yaWdpbmFsXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gPD57Zm9ybWF0TWVtYmVyc1RvTm90aWZ5KGluY2lkZW50Lm1lbWJlcnNUb05vdGlmeSl9PC8+XHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiAndmVzc2VsJyxcclxuICAgICAgICAgICAgaGVhZGVyOiAoeyBjb2x1bW4gfTogeyBjb2x1bW46IGFueSB9KSA9PiAoXHJcbiAgICAgICAgICAgICAgICA8RGF0YVRhYmxlU29ydEhlYWRlciBjb2x1bW49e2NvbHVtbn0gdGl0bGU9XCJWZXNzZWxcIiAvPlxyXG4gICAgICAgICAgICApLFxyXG4gICAgICAgICAgICBjZWxsQWxpZ25tZW50OiAnbGVmdCcgYXMgY29uc3QsXHJcbiAgICAgICAgICAgIGJyZWFrcG9pbnQ6ICd0YWJsZXQtbWQnIGFzIGNvbnN0LFxyXG4gICAgICAgICAgICBjZWxsQ2xhc3NOYW1lOiAncHgtMi41JyxcclxuICAgICAgICAgICAgY2VsbDogKHsgcm93IH06IHsgcm93OiBhbnkgfSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgaW5jaWRlbnQgPSByb3cub3JpZ2luYWxcclxuXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtpbmNpZGVudC52ZXNzZWwgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFZlc3NlbExvY2F0aW9uRGlzcGxheVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbD17Z2V0VmVzc2VsV2l0aEljb24oXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluY2lkZW50LnZlc3NlbC5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5jaWRlbnQudmVzc2VsLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmVzc2VsSWQ9e2luY2lkZW50LnZlc3NlbC5pZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5VGV4dD17aW5jaWRlbnQudmVzc2VsLnRpdGxlIHx8ICcnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgc29ydGluZ0ZuOiAocm93QTogYW55LCByb3dCOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlQSA9IHJvd0E/Lm9yaWdpbmFsPy52ZXNzZWw/LnRpdGxlIHx8ICcnXHJcbiAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZUIgPSByb3dCPy5vcmlnaW5hbD8udmVzc2VsPy50aXRsZSB8fCAnJ1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlQS5sb2NhbGVDb21wYXJlKHZhbHVlQilcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6ICdyZXBvcnRlZEJ5JyxcclxuICAgICAgICAgICAgaGVhZGVyOiAoeyBjb2x1bW4gfTogeyBjb2x1bW46IGFueSB9KSA9PiAoXHJcbiAgICAgICAgICAgICAgICA8RGF0YVRhYmxlU29ydEhlYWRlciBjb2x1bW49e2NvbHVtbn0gdGl0bGU9XCJSZXBvcnRlZCBCeVwiIC8+XHJcbiAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgIGJyZWFrcG9pbnQ6ICd0YWJsZXQtbWQnIGFzIGNvbnN0LFxyXG4gICAgICAgICAgICBjZWxsQ2xhc3NOYW1lOiAncHgtMi41JyxcclxuICAgICAgICAgICAgY2VsbDogKHsgcm93IH06IHsgcm93OiBhbnkgfSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgaW5jaWRlbnQgPSByb3cub3JpZ2luYWxcclxuICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICAgICAge2luY2lkZW50LnJlcG9ydGVkQnkgJiYgaW5jaWRlbnQucmVwb3J0ZWRCeS5pZCA+IDAgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMi41XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRvb2x0aXAgbW9iaWxlQ2xpY2thYmxlPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VG9vbHRpcFRyaWdnZXIgbW9iaWxlQ2xpY2thYmxlPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEF2YXRhciBjbGFzc05hbWU9XCJoLTggdy04XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEF2YXRhckZhbGxiYWNrIGNsYXNzTmFtZT1cInRleHQteHNcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2dldENyZXdJbml0aWFscyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluY2lkZW50LnJlcG9ydGVkQnlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuZmlyc3ROYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5jaWRlbnQucmVwb3J0ZWRCeS5zdXJuYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQXZhdGFyRmFsbGJhY2s+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0F2YXRhcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwVHJpZ2dlcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRvb2x0aXBDb250ZW50IGNsYXNzTmFtZT1cImxnOmhpZGRlblwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdE5hbWUoaW5jaWRlbnQucmVwb3J0ZWRCeSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVG9vbHRpcENvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImhpZGRlbiBsZzpibG9ja1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0TmFtZShpbmNpZGVudC5yZXBvcnRlZEJ5KX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgc29ydGluZ0ZuOiAocm93QTogYW55LCByb3dCOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlQSA9IGZvcm1hdE5hbWUocm93QT8ub3JpZ2luYWw/LnJlcG9ydGVkQnkpIHx8ICcnXHJcbiAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZUIgPSBmb3JtYXROYW1lKHJvd0I/Lm9yaWdpbmFsPy5yZXBvcnRlZEJ5KSB8fCAnJ1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlQS5sb2NhbGVDb21wYXJlKHZhbHVlQilcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6ICdzdGFydERhdGUnLFxyXG4gICAgICAgICAgICBoZWFkZXI6ICh7IGNvbHVtbiB9OiB7IGNvbHVtbjogYW55IH0pID0+IChcclxuICAgICAgICAgICAgICAgIDxEYXRhVGFibGVTb3J0SGVhZGVyIGNvbHVtbj17Y29sdW1ufSB0aXRsZT1cIlN0YXJ0IERhdGVcIiAvPlxyXG4gICAgICAgICAgICApLFxyXG4gICAgICAgICAgICBjZWxsQWxpZ25tZW50OiAnbGVmdCcgYXMgY29uc3QsXHJcbiAgICAgICAgICAgIGJyZWFrcG9pbnQ6ICdsYXB0b3AnIGFzIGNvbnN0LFxyXG4gICAgICAgICAgICBjZWxsQ2xhc3NOYW1lOiAncHgtMi41JyxcclxuICAgICAgICAgICAgY2VsbDogKHsgcm93IH06IHsgcm93OiBhbnkgfSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgaW5jaWRlbnQgPSByb3cub3JpZ2luYWxcclxuICAgICAgICAgICAgICAgIHJldHVybiA8Pntmb3JtYXREYXRlKGluY2lkZW50LnN0YXJ0RGF0ZSl9PC8+XHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIHNvcnRpbmdGbjogKHJvd0E6IGFueSwgcm93QjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZUEgPSByb3dBPy5vcmlnaW5hbD8uc3RhcnREYXRlIHx8ICcnXHJcbiAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZUIgPSByb3dCPy5vcmlnaW5hbD8uc3RhcnREYXRlIHx8ICcnXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWVBLmxvY2FsZUNvbXBhcmUodmFsdWVCKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICBhY2Nlc3NvcktleTogJ2VuZERhdGUnLFxyXG4gICAgICAgICAgICBoZWFkZXI6ICh7IGNvbHVtbiB9OiB7IGNvbHVtbjogYW55IH0pID0+IChcclxuICAgICAgICAgICAgICAgIDxEYXRhVGFibGVTb3J0SGVhZGVyIGNvbHVtbj17Y29sdW1ufSB0aXRsZT1cIkVuZCBEYXRlXCIgLz5cclxuICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgY2VsbEFsaWdubWVudDogJ2xlZnQnIGFzIGNvbnN0LFxyXG4gICAgICAgICAgICBicmVha3BvaW50OiAnbGFwdG9wJyBhcyBjb25zdCxcclxuICAgICAgICAgICAgY2VsbENsYXNzTmFtZTogJ3B4LTIuNScsXHJcbiAgICAgICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogYW55IH0pID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGluY2lkZW50ID0gcm93Lm9yaWdpbmFsXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gPD57Zm9ybWF0RGF0ZShpbmNpZGVudC5lbmREYXRlKX08Lz5cclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgc29ydGluZ0ZuOiAocm93QTogYW55LCByb3dCOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlQSA9IHJvd0E/Lm9yaWdpbmFsPy5lbmREYXRlIHx8ICcnXHJcbiAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZUIgPSByb3dCPy5vcmlnaW5hbD8uZW5kRGF0ZSB8fCAnJ1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlQS5sb2NhbGVDb21wYXJlKHZhbHVlQilcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgXSlcclxuXHJcbiAgICByZXR1cm4gKFxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIHB5LTBcIj5cclxuICAgICAgICAgICAgPExpc3RIZWFkZXJcclxuICAgICAgICAgICAgICAgIGljb249ezxTZWFsb2dzSW5jaWRlbnRJY29uIGNsYXNzTmFtZT1cInctOCBoLThcIiAvPn1cclxuICAgICAgICAgICAgICAgIHRpdGxlPVwiSW5jaWRlbnQgUmVjb3Jkc1wiXHJcbiAgICAgICAgICAgICAgICBhY3Rpb25zPXtcclxuICAgICAgICAgICAgICAgICAgICA8RHJvcGRvd25NZW51PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8RHJvcGRvd25NZW51VHJpZ2dlciBhc0NoaWxkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlYWxvZ3NDb2dJY29uIHNpemU9ezM2fSAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0Ryb3Bkb3duTWVudVRyaWdnZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPERyb3Bkb3duTWVudUl0ZW1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJvdXRlci5wdXNoKCcvaW5jaWRlbnQtcmVjb3Jkcy9jcmVhdGUnKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIE5ldyBpbmNpZGVudCByZWNvcmRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51SXRlbT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51PlxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAvPlxyXG5cclxuICAgICAgICAgICAgey8qIENoYXJ0IERhc2hib2FyZCAqL31cclxuICAgICAgICAgICAgPEluY2lkZW50Q2hhcnRzRGFzaGJvYXJkIC8+XHJcblxyXG4gICAgICAgICAgICB7LyogVGFibGUgd2l0aCBpbnRlZ3JhdGVkIGZpbHRlcnMgKi99XHJcbiAgICAgICAgICAgIDxEYXRhVGFibGVcclxuICAgICAgICAgICAgICAgIGNvbHVtbnM9e2NvbHVtbnN9XHJcbiAgICAgICAgICAgICAgICBkYXRhPXtpbmNpZGVudFJlY29yZHN9XHJcbiAgICAgICAgICAgICAgICBub0RhdGFUZXh0PVwiTm8gaW5jaWRlbnQgcmVjb3JkcyBmb3VuZFwiXHJcbiAgICAgICAgICAgICAgICBzaG93VG9vbGJhcj17dHJ1ZX1cclxuICAgICAgICAgICAgICAgIGlzTG9hZGluZz17aXNMb2FkaW5nIHx8IHF1ZXJ5SW5jaWRlbnRSZWNvcmRzTG9hZGluZ31cclxuICAgICAgICAgICAgICAgIHBhZ2VTaXplPXsxMH1cclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVGaWx0ZXJDaGFuZ2V9XHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICApXHJcbn1cclxuZXhwb3J0IGRlZmF1bHQgSW5jaWRlbnRSZWNvcmRMaXN0XHJcbiJdLCJuYW1lcyI6WyJ1c2VSb3V0ZXIiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsInVzZUxhenlRdWVyeSIsIkdFVF9JTkNJREVOVF9SRUNPUkRTIiwiZGF5anMiLCJmb3JtYXREYXRlIiwiTGluayIsIkRhdGFUYWJsZSIsImNyZWF0ZUNvbHVtbnMiLCJEYXRhVGFibGVTb3J0SGVhZGVyIiwiTGlzdEhlYWRlciIsIkF2YXRhciIsIkF2YXRhckZhbGxiYWNrIiwiVmVzc2VsTG9jYXRpb25EaXNwbGF5IiwiU2VhbG9nc0luY2lkZW50SWNvbiIsIkluY2lkZW50Q2hhcnRzRGFzaGJvYXJkIiwidXNlVmVzc2VsSWNvbkRhdGEiLCJEcm9wZG93bk1lbnUiLCJEcm9wZG93bk1lbnVDb250ZW50IiwiRHJvcGRvd25NZW51VHJpZ2dlciIsIlAiLCJTZXBhcmF0b3IiLCJUb29sdGlwIiwiVG9vbHRpcENvbnRlbnQiLCJUb29sdGlwVHJpZ2dlciIsIlNlYWxvZ3NDb2dJY29uIiwiRHJvcGRvd25NZW51SXRlbSIsImdldENyZXdJbml0aWFscyIsImZpcnN0TmFtZSIsInN1cm5hbWUiLCJmaXJzdCIsImNoYXJBdCIsInRvVXBwZXJDYXNlIiwibGFzdCIsImZvcm1hdE5hbWUiLCJwZXJzb24iLCJpZCIsImZvcm1hdE1lbWJlcnNUb05vdGlmeSIsIm1lbWJlcnMiLCJub2RlcyIsImxlbmd0aCIsIm1hcCIsIm1lbWJlciIsImpvaW4iLCJJbmNpZGVudFJlY29yZExpc3QiLCJyb3V0ZXIiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJpbmNpZGVudFJlY29yZHMiLCJzZXRJbmNpZGVudFJlY29yZHMiLCJmaWx0ZXIiLCJzZXRGaWx0ZXIiLCJnZXRWZXNzZWxXaXRoSWNvbiIsImdldEluY2lkZW50UmVjb3JkcyIsImxvYWRpbmciLCJxdWVyeUluY2lkZW50UmVjb3Jkc0xvYWRpbmciLCJmZXRjaFBvbGljeSIsIm9uQ29tcGxldGVkIiwicmVzcG9uc2UiLCJyZWFkSW5jaWRlbnRSZWNvcmRzIiwib25FcnJvciIsImVycm9yIiwiY29uc29sZSIsImxvYWRJbmNpZGVudFJlY29yZHMiLCJ2YXJpYWJsZXMiLCJsaW1pdCIsIk9iamVjdCIsImtleXMiLCJncmFwaHFsRmlsdGVyIiwidmVzc2VsIiwidmFsdWUiLCJ2ZXNzZWxJRCIsImVxIiwiZGF0ZVJhbmdlIiwic3RhcnREYXRlIiwiZ3RlIiwic3RhcnRPZiIsInRvSVNPU3RyaW5nIiwiZW5kRGF0ZSIsImx0ZSIsImVuZE9mIiwiaGFuZGxlRmlsdGVyQ2hhbmdlIiwidHlwZSIsImRhdGEiLCJwcmV2RmlsdGVyIiwiY29sdW1ucyIsImFjY2Vzc29yS2V5IiwiaGVhZGVyIiwiY29sdW1uIiwidGl0bGUiLCJjZWxsQ2xhc3NOYW1lIiwiY2VsbCIsInJvdyIsImluY2lkZW50Iiwib3JpZ2luYWwiLCJkaXYiLCJjbGFzc05hbWUiLCJocmVmIiwibWVtYmVyc1RvTm90aWZ5IiwicCIsInZlc3NlbElkIiwiZGlzcGxheVRleHQiLCJvcmllbnRhdGlvbiIsInJlcG9ydGVkQnkiLCJzb3J0aW5nRm4iLCJyb3dBIiwicm93QiIsInZhbHVlQSIsInZhbHVlQiIsImxvY2FsZUNvbXBhcmUiLCJjZWxsQWxpZ25tZW50IiwiYnJlYWtwb2ludCIsIm1vYmlsZUNsaWNrYWJsZSIsInNwYW4iLCJpY29uIiwiYWN0aW9ucyIsImFzQ2hpbGQiLCJzaXplIiwib25DbGljayIsInB1c2giLCJub0RhdGFUZXh0Iiwic2hvd1Rvb2xiYXIiLCJwYWdlU2l6ZSIsIm9uQ2hhbmdlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/incident-record/incident-record-list.tsx\n"));

/***/ })

});